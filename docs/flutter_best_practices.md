# Flutter Best Practices

This document outlines the best practices to follow for developing and maintaining this Flutter application. Adhering to these guidelines will help ensure clean code, reusability, and a more maintainable codebase.

## 1. Project Structure

-   **Organization:** We will primarily follow a **feature-first** approach for organizing files and folders. Each significant feature will reside in its own directory within `lib/features/`.
    -   Inside each feature directory, consider subdirectories like `data` (repositories, data sources, models), `domain` (use cases, entities, if applicable), `presentation` (widgets/pages, bloc/cubit/provider), and `widgets` (feature-specific widgets).
-   **Common Code:** Code shared across multiple features (e.g., utility classes, core services, generic widgets) should be placed in directories like `lib/common/` or `lib/core/`. For instance:
    -   `lib/core/services/`
    -   `lib/common/widgets/`
    -   `lib/common/utils/`
-   **`main.dart`:** Keep `main.dart` as clean as possible, primarily for app initialization and routing setup.

## 2. Naming Conventions

-   **Files:** Use `snake_case` (e.g., `user_profile_page.dart`, `auth_service.dart`). Widget files can also be suffixed with `_widget.dart` or `_screen.dart` or `_page.dart` for clarity.
-   **Classes:** Use `PascalCase` (e.g., `UserProfilePage`, `AuthService`, `PostBloc`).
-   **Variables and Methods:** Use `camelCase` (e.g., `userName`, `fetchUserData()`).
-   **Constants:** Use `camelCase` or `UPPER_SNAKE_CASE` for top-level or static constants (e.g., `defaultTimeout`, `API_KEY`).
-   **Enums:** Use `PascalCase` for enum names and `camelCase` for enum values (e.g., `enum AuthStatus { authenticated, unauthenticated }`).

## 3. Widget Design

-   **Stateless vs. Stateful:** Prefer `StatelessWidget` whenever possible. Only use `StatefulWidget` when the widget manages its own internal, mutable state that doesn't affect the rest of the app.
-   **Widget Granularity:** Break down large widgets into smaller, more manageable, and reusable components. Each widget should have a single responsibility.
-   **`const` Constructors:** Use `const` constructors for widgets where possible to improve performance by allowing Flutter to cache and reuse widget instances.
-   **Build Method Purity:** The `build` method should be pure and free of side effects. It should only depend on the widget's configuration, `BuildContext`, and `State` (for `StatefulWidget`).
-   **Avoid Business Logic in Widgets:** Widgets should primarily focus on rendering UI based on the current state. Business logic should reside in BLoCs, Cubits, Providers, or services.

## 4. State Management

-   **Clear Strategy:**
    -   **Provider:** Use `Provider` (specifically `ChangeNotifierProvider`) for managing local widget tree state or simple app-wide state. It's suitable for state that doesn't involve complex business rules or asynchronous operations directly within the notifier.
    -   **BLoC/Cubit:** Use `flutter_bloc` (BLoC or Cubit) for managing more complex state, especially when dealing with business logic, asynchronous operations, and a clear separation of events/states. Cubit is a simpler version of BLoC.
    -   **Riverpod/GetX (Consideration for Future):** While we currently use Provider and BLoC, future consideration could be given to other state management solutions if deemed beneficial. For now, consistency with Provider and BLoC is key.
-   **Separation of Concerns:**
    -   **UI Layer:** Widgets that display state and dispatch events/call methods.
    -   **State Management Layer:** BLoCs, Cubits, or ChangeNotifiers that manage the state and contain business logic.
    -   **Data Layer:** Repositories and services that fetch and manipulate data.
-   **Immutability:** When using BLoC or other state management solutions that benefit from it, ensure that states are immutable. When a state changes, create a new state object.

## 5. Navigation

-   **Centralized Navigation:** Utilize `GoRouter` for all navigation. Define named routes in a centralized location (e.g., `app_routes.dart`).
-   **Avoid Direct `Navigator.push`:** Replace direct calls to `Navigator.push`, `Navigator.pop`, etc., with `GoRouter`'s methods like `context.go()`, `context.push()`, `context.pop()`. This improves testability and deep linking.
-   **Route Parameters:** Pass arguments between routes using `GoRouter`'s parameter mechanisms (path parameters, query parameters, or extra).

## 6. Asynchronous Operations

-   **`async/await`:** Use `async/await` for all asynchronous operations to improve code readability.
-   **Error Handling:** Always include `try-catch` blocks for asynchronous operations that might fail (e.g., API calls, file I/O).
-   **State Updates:**
    -   In BLoCs/Cubits: Handle async operations within the BLoC/Cubit and emit new states to reflect loading, success, or error.
    -   In Providers: Perform async operations in methods within your `ChangeNotifier` and call `notifyListeners()` upon completion or error.
    -   In Widgets (less ideal for complex logic): Use `FutureBuilder` or `StreamBuilder` for simple cases where UI needs to react to an async operation directly. However, prefer to move complex async logic to state managers.

## 7. Dependency Injection

-   **`GetIt`:** Use `GetIt` as a service locator to register and retrieve dependencies (services, repositories, BLoCs). This promotes loose coupling and testability.
-   **Provider for Scoped Dependencies:** `Provider` can also be used for dependency injection, especially for providing dependencies down the widget tree (e.g., `RepositoryProvider` from `flutter_bloc`).
-   **Constructor Injection:** Prefer constructor injection for passing dependencies to classes.

## 8. Error Handling

-   **Specific Error Types:** Define custom error classes or use existing ones to represent different error scenarios.
-   **User Feedback:** Provide clear feedback to the user in case of errors (e.g., using `BotToast` for snackbars/toasts, or dedicated error widgets).
-   **Logging:** Use a consistent logging strategy (e.g., the `logging` package, or a custom logger wrapper) to log errors and important events.
-   **Crash Reporting:** Integrate a crash reporting tool like Sentry (already in use) to capture and analyze crashes in production.

## 9. Code Comments and Documentation

-   **Clarity over Quantity:** Write comments that explain _why_ something is done, not just _what_ is done (if the code itself isn't self-explanatory).
-   **Dart Doc:** Use Dart Doc comments (`///`) for all public classes, methods, and properties. This allows for generating documentation and improves IDE support.
-   **`TODO` Comments:** Use `// TODO:` for tasks that need to be done later, and consider linking to an issue tracker if applicable.
    -   **Descriptive TODOs:** Make TODOs specific and descriptive. Include enough context for any developer to understand what needs to be done.
    -   **Link to Issue Tracker:** When possible, reference an issue number (e.g., `// TODO: #123 - Implement pagination for user list`).
    -   **Common TODO Scenarios:** Add TODOs for:
        -   Mock data usage (e.g., `// TODO: Replace with real API data once backend is ready`)
        -   Non-functional UI elements (e.g., `// TODO: Implement real navigation for this button`)
        -   Temporary workarounds (e.g., `// TODO: This is a temporary fix, needs proper implementation by [date]`)
        -   Performance optimizations (e.g., `// TODO: Optimize rendering for large lists`)
        -   Security concerns (e.g., `// TODO: Add proper input validation`)
    -   **Regular Reviews:** Schedule regular reviews of TODOs in the codebase to ensure they don't accumulate indefinitely.

## 10. Testing

-   **Unit Tests:** Write unit tests for business logic in BLoCs, Cubits, Providers, services, and utility functions. Aim for high test coverage for these critical parts.
-   **Widget Tests:** Write widget tests for individual widgets to verify UI rendering and interactions.
-   **Integration Tests:** Write integration tests for user flows and feature interactions.
-   **Mocking:** Use mocking libraries like `mockito` or `mocktail` to mock dependencies in tests.

## 11. Reusability

-   **Generic Widgets:** Create a library of common, reusable widgets (e.g., custom buttons, input fields, loading indicators) in `lib/common/widgets/`.
-   **Utility Functions:** Group reusable helper functions in utility classes (e.g., `lib/common/utils/date_formatter.dart`).
-   **Services:** Design services to be reusable across different features (e.g., `AuthService`, `ApiService`).
-   **Extensions:** Use Dart extensions to add reusable functionality to existing classes.

## 12. Linting

-   **`analysis_options.yaml`:** Configure `analysis_options.yaml` with strict linting rules. Start with `flutter_lints` and consider adding more specific rules from packages like `lints`.
-   **Code Formatting:** Ensure code is formatted consistently using `dart format`. This is usually handled by the IDE.

## 13. Environment Configuration

-   **`flutter_dotenv`:** Continue using `flutter_dotenv` to manage environment-specific configurations (API keys, base URLs, etc.).
-   **Separate `.env` files:** Maintain separate `.env` files for different environments (e.g., `.env.development`, `.env.production`). Ensure these files are listed in `.gitignore`.
-   **Configuration Service:** Consider a dedicated service or class to provide easy access to environment variables throughout the app, rather than calling `dotenv.env[]` directly everywhere.

## 14. API Client

-   **Generated Clients (Recommended for REST APIs):** If the project heavily relies on REST APIs, consider using code generation tools (e.g., `retrofit`, `chopper`, or OpenAPI Generator with the `dio_generator` package) to generate API client code. This reduces boilerplate, improves type safety, and makes API updates easier. (The project already uses generated code for `OpenAI_ChatBot_API_prod_swagger`, which is good).
-   **Error Handling in API Client:** Implement robust error handling within the API client, mapping HTTP error codes to specific error types.

## 15. Model Class Design

-   **Immutability:** Prefer immutable data classes for models. Once an object is created, it should not be changed. If modifications are needed, create a new instance with the updated values.
    -   Use `final` properties in your model classes.
    -   Ensure constructors take all necessary properties.
-   **`copyWith` Method:** For immutable classes, implement a `copyWith` method to easily create new instances with modified fields.
-   **Serialization/Deserialization:** Use `json_serializable` for converting Dart objects to and from JSON.
-   **`Equatable` or `Freezed`:**
    -   **`equatable`:** Override `==` and `hashCode` easily to compare model instances by value rather than by reference.
    -   **`freezed`:** A more powerful alternative that generates immutable classes, `copyWith`, `==`, `hashCode`, `toString`, and serialization/deserialization helpers. Consider migrating models to `freezed` for enhanced robustness and developer experience.

## 16. Build Context Safety

-   **Async Gaps:** Be cautious when using `BuildContext` after an `async` gap (an `await` statement). If a widget might have been unmounted during the async operation, check `if (mounted)` before accessing its `BuildContext` or `State`. This is especially important in `StatefulWidget`s.

## 17. Platform-Specific Code

-   **Conditional Imports/Exports:** Use conditional imports/exports for platform-specific implementations if necessary.
-   **Platform Channels:** If extensive platform-specific functionality is required, use Flutter's platform channels, keeping the channel communication well-defined and minimal.

## 18. User Interface Guidelines

-   **Consistent Design System:** Create and follow a consistent design system with predefined colors, text styles, spacing, and component variants.
-   **Accessibility:**
    -   Use semantic labels for widgets (`semanticLabel` property) to support screen readers.
    -   Ensure sufficient color contrast for text and interactive elements.
    -   Support dynamic text sizing for users with vision impairments.
    -   Implement proper focus traversal for keyboard navigation.
-   **UI Affordances and Hints:**
    -   **Provide Visual Cues:** Use visual indicators to show when elements are interactive (e.g., hover effects, ripple animations).
    -   **Tooltips:** Add tooltips (`Tooltip` widget) to icons and buttons that may not be self-explanatory.
    -   **Input Hints:** For text fields, provide clear hint texts and validation error messages.
    -   **Empty States:** Design informative empty states for lists, search results, etc.
    -   **Loading States:** Show loading indicators when data is being fetched or processed.
    -   **Confirmation Dialogs:** Use confirmation dialogs for destructive or important actions.
    -   **Onboarding:** Consider implementing onboarding flows or tutorials for complex features.
    -   **Error Recovery:** Provide actionable error messages with recovery options when possible.
-   **Responsive Design:**
    -   Use `LayoutBuilder`, `MediaQuery`, or packages like `flutter_screenutil` to create responsive layouts.
    -   Test the UI on different screen sizes and orientations.
    -   Consider implementing different layouts for mobile, tablet, and desktop if applicable.

## 19. Data Presentation

-   **List and Table UX:**
    -   **Pagination:** Implement pagination for large data sets using packages like `infinite_scroll_pagination` or custom solutions.
    -   **Search Functionality:** Add search bars with debounce for filtering data collections.
    -   **Sorting:** Allow users to sort data by different columns or properties.
    -   **Filtering:** Implement filter options that allow users to narrow down data based on various criteria.
    -   **Selection:** Support single or multiple item selection with visual indicators.
    -   **Pull-to-Refresh:** Implement pull-to-refresh for lists that display real-time or frequently updated data.
    -   **Lazy Loading:** Use lazy loading techniques for images and other heavy content.
    -   **Empty and Error States:** Design user-friendly empty states and error handling for data presentation widgets.
-   **Data Visualization:**
    -   Consider using chart libraries like `fl_chart`, `syncfusion_flutter_charts`, or `charts_flutter` for data visualization needs.
    -   Ensure visualizations are accessible with proper labels and alternative text representations.
-   **Performance Considerations:**
    -   Use `ListView.builder` or `GridView.builder` instead of non-builder variants for better performance with large datasets.
    -   Implement caching mechanisms for frequently accessed data.
    -   Consider pagination or infinite scrolling instead of loading all data at once.
    -   Use `const` widgets and memoization techniques to minimize rebuilds.
-   **State Indication:**
    -   Show clear loading states during data fetching.
    -   Implement skeleton screens instead of simple spinners for better user experience.
    -   Design informative error states with retry options.

## 20. Data Layer Architecture

### 20.1 Models

-   **Naming Conventions:**
    -   Use singular nouns for model class names (e.g., `User`, `Message`, `Product`).
    -   Suffix with `Model` when clarity is needed (e.g., `UserModel`, `ProductModel`).
    -   For DTOs (Data Transfer Objects), use appropriate suffixes like `Response`, `Request`, or `Dto` (e.g., `LoginRequest`, `UserResponse`).
-   **Location:**
    -   Feature-specific models: `lib/features/[feature_name]/data/models/`
    -   Shared models: `lib/core/models/` or `lib/common/models/`
-   **Structure:**
    -   **Entity vs. Model:** Consider separating domain entities (`lib/features/[feature_name]/domain/entities/`) from data models (`lib/features/[feature_name]/data/models/`) in complex applications. Entities represent core business objects, while models handle serialization/deserialization.
    -   **From/To JSON:** Always implement `fromJson` and `toJson` methods (ideally using `json_serializable`).
    -   **Named Constructors:** Use named constructors for alternative creation patterns (e.g., `User.fromFirestore()`, `User.empty()`).
-   **Best Practices:**
    -   **Validation:** Include validation logic in constructors or factory methods.
    -   **Default Values:** Provide sensible defaults for optional fields.
    -   **Type Safety:** Use non-nullable types when appropriate, and avoid using `dynamic` unless necessary.
    -   **Documentation:** Document each field, especially those with non-obvious purposes.
    -   **Testing:** Write unit tests for serialization/deserialization and any business logic in models.

### 20.2 Repositories

-   **Naming Conventions:**
    -   Use descriptive names with `Repository` suffix (e.g., `UserRepository`, `ProductRepository`).
    -   Interface (abstract class) should follow the same naming convention, optionally with an `I` prefix (e.g., `IUserRepository` or just `UserRepository`).
    -   Implementation classes should indicate their source if multiple implementations exist (e.g., `FirebaseUserRepository`, `ApiUserRepository`).
-   **Location:**
    -   Feature-specific repositories: `lib/features/[feature_name]/data/repositories/`
    -   Shared repositories: `lib/core/repositories/` or `lib/common/repositories/`
-   **Structure:**
    -   **Interfaces:** Define repository interfaces as abstract classes with clear method signatures.
    -   **Implementations:** Create concrete implementations that implement these interfaces.
    -   **Data Sources:** Repositories should depend on data sources, not directly on API clients or database helpers.
-   **Best Practices:**
    -   **Single Responsibility:** Each repository should handle a single entity or closely related group of entities.
    -   **Abstraction:** Repositories should abstract away data source details, providing a clean API for business logic.
    -   **Error Handling:** Catch source-specific errors and translate them to domain-specific errors.
    -   **Caching Strategy:** Implement appropriate caching strategies (memory cache, local storage).
    -   **Return Types:** Return domain entities rather than data models when using the entity/model separation.
    -   **Testing:** Mock data sources to test repository logic in isolation.
    -   **Dependency Injection:** Use constructor injection for data sources and other dependencies.

```
// Example repository interface
abstract class UserRepository {
  Future<User> getUserById(String id);
  Future<List<User>> getAllUsers();
  Future<void> saveUser(User user);
  Future<void> deleteUser(String id);
}

// Example implementation
class FirebaseUserRepository implements UserRepository {
  final FirebaseUserDataSource _dataSource;

  FirebaseUserRepository(this._dataSource);

  @override
  Future<User> getUserById(String id) async {
    try {
      final userModel = await _dataSource.getUserById(id);
      return User.fromModel(userModel);
    } on FirebaseException catch (e) {
      throw UserNotFoundException('User not found: $id');
    }
  }

  // Additional implementation...
}
```

### 20.3 Services

-   **Naming Conventions:**
    -   Use descriptive names with `Service` suffix (e.g., `AuthService`, `AnalyticsService`).
    -   Interface (abstract class) should follow the same naming convention, optionally with an `I` prefix (e.g., `IAuthService` or just `AuthService`).
    -   Implementation classes can be prefixed with platform or implementation details if multiple implementations exist (e.g., `FirebaseAuthService`).
-   **Location:**
    -   Feature-specific services: `lib/features/[feature_name]/domain/services/` or `lib/features/[feature_name]/services/`
    -   Shared services: `lib/core/services/` or `lib/common/services/`
-   **Types of Services:**
    -   **Infrastructure Services:** Handle system-level concerns (e.g., `ConnectivityService`, `LocalStorageService`).
    -   **Domain Services:** Implement business logic that doesn't naturally fit into a model or entity (e.g., `PaymentService`, `RecommendationService`).
    -   **Utility Services:** Provide helper functionality (e.g., `DateTimeService`, `FormatService`).
    -   **Integration Services:** Wrap third-party SDKs or APIs (e.g., `FirebaseService`, `AnalyticsService`).
-   **Best Practices:**
    -   **Interface-First Design:** Define service interfaces before implementations to focus on what the service does rather than how it does it.
    -   **Statelessness:** Services should generally be stateless, with any state managed externally or persisted.
    -   **Composability:** Break down complex services into smaller, focused services.
    -   **Error Handling:** Handle and translate errors into domain-specific exceptions.
    -   **Testing:** Mock dependencies to test service logic in isolation.
    -   **Configuration:** Accept configuration parameters in constructors rather than hardcoding values.

```
// Example service interface
abstract class AuthService {
  Future<User> signIn(String email, String password);
  Future<void> signOut();
  Stream<User?> get authStateChanges;
  User? get currentUser;
}

// Example implementation
class FirebaseAuthService implements AuthService {
  final FirebaseAuth _firebaseAuth;
  final UserRepository _userRepository;

  FirebaseAuthService(this._firebaseAuth, this._userRepository);

  @override
  Future<User> signIn(String email, String password) async {
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      final userId = credential.user?.uid;
      if (userId == null) throw AuthException('Sign in failed');

      return await _userRepository.getUserById(userId);
    } on FirebaseAuthException catch (e) {
      throw AuthException('Authentication failed: ${e.message}');
    }
  }

  // Additional implementation...
}
```

### 20.4 Integration between Models, Repositories, and Services

-   **Data Flow Pattern:**
    -   **UI → BLoC/Cubit/Provider → Service → Repository → Data Source → External System**
    -   **External System → Data Source → Repository → Service → BLoC/Cubit/Provider → UI**
-   **Best Practices:**

    -   **Clear Boundaries:** Maintain clear boundaries between layers.
    -   **Dependency Direction:** Dependencies should point inward (UI depends on BLoC, BLoC depends on service, service depends on repository).
    -   **Data Transformation:** Transform data between layers as appropriate (e.g., model to entity in repositories, entity to UI model in BLoCs).
    -   **Consistent Error Handling:** Each layer should handle errors appropriately for its level of abstraction.
    -   **Unit Testing:** Test each layer in isolation with mocked dependencies.
    -   **Integration Testing:** Test interactions between layers to ensure proper integration.

-   **Example Flow:**
    1. UI dispatches an event to a BLoC
    2. BLoC calls a service method
    3. Service coordinates between repositories
    4. Repository calls data sources
    5. Data sources interact with external systems
    6. Response flows back up through the layers, with appropriate transformations at each step

This document is a living guide and should be updated as the project evolves and new best practices emerge.
