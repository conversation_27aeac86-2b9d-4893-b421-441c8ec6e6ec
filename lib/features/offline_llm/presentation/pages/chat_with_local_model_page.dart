import 'dart:convert';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/app_preferences.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/manage_model_page.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/desktop/pages/home_page.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/mobile/pages/home_page.dart';
import 'package:diogeneschatbot/models/conversation.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/models/offline_model.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gemma/flutter_gemma.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:intl/intl.dart';

// TODO: Add conversation export/backup functionality
// TODO: Implement message search and filtering
// TODO: Add conversation templates for quick starts
// TODO: Implement model performance monitoring
// TODO: Add conversation sharing capabilities
// TODO: Implement advanced model settings (temperature, top-k, etc.)
// TODO: Add conversation history management
// TODO: Implement message reactions and favorites

// import 'package:mediapipe_genai/mediapipe_genai.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

// import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:get/get.dart';

/// Enhanced offline LLM chat page with modern UI and improved user experience
class ChatWithLocalModelPage extends StatefulWidget {
  const ChatWithLocalModelPage({super.key});

  @override
  State<ChatWithLocalModelPage> createState() => _ChatWithLocalModelPageState();
}

class _ChatWithLocalModelPageState extends State<ChatWithLocalModelPage> {
  // final FlutterSecureStorage storage = FlutterSecureStorage();
  Map<String, Model> _models = {};
  String? _selectedModel;
  final TextEditingController _messageController = TextEditingController();

  // Enhanced: Track conversation state and UI preferences
  bool _showAdvancedSettings = false;

  // List<String> _chatMessages = [];
  Conversation? _conversation;
  String botId = Uuid().v4();
  bool _isLoading = false; // Add this line

  // Add this variable to store the previous model's original filename
  String? _previousModelName;
  String _streamingResponse = ""; // New variable to hold the streaming response
  static String offlineModelsKey = "offline_models";

  // late LlmInferenceEngine engine;

  @override
  void initState() {
    super.initState();
    _loadModels();
    _conversation = Conversation(
      id: Uuid().v4(), // Generate a new UUID for the conversation
      botId: botId,
      messages: [],
      createdAt: DateTime.now(),
    );
  }

  @override
  void dispose() {
    if (_selectedModel != null) {
      // Check if model.bin exists and if _previousModelName is not null
      final file = File(_models[_selectedModel!]!.filePath);
      final modelBinFile = File(
        file.parent.path + '/model.bin',
      ); // Use the actual path to model.bin
      if (modelBinFile.existsSync() && _previousModelName != null) {
        // Rename model.bin back to the previous model's real name
        modelBinFile.renameSync(_previousModelName!);
      }
    }
    super.dispose();
  }

  void _onModelSelected(String? newValue) {
    if (newValue != null) {
      Model? selectedModel = _models[newValue]!;
      // Check if model.bin exists and if _previousModelName is not null
      final file = File(selectedModel!.filePath);
      final modelBinFile = File(
        file.parent.path + '/model.bin',
      ); // Use the actual path to model.bin
      if (modelBinFile.existsSync() && _previousModelName != null) {
        // Rename model.bin back to the previous model's real name
        modelBinFile.renameSync(_previousModelName!);
      }

      _previousModelName =
          selectedModel!.filePath; // Store the previous model's real name

      // Set the new selected model and store its real name
      setState(() {
        _selectedModel = selectedModel.name;
      });

      // Initialize Gemma with the selected model's path
      _initializeGemma(selectedModel); // Call the init method
    }
  }

  Future<void> _loadModels() async {
    final stopwatch = Stopwatch()..start();
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();

      // Get the list of model JSON strings
      String? modelJsonString = prefs.getString(offlineModelsKey);
      if (modelJsonString != null) {
        // Decode the list of model JSON strings
        List<dynamic> modelJsonList = jsonDecode(modelJsonString);
        for (var json in modelJsonList) {
          Model model = Model.fromJson(jsonDecode(json));
          if (model.isDownloaded) {
            _models[model.name] = model;
          }
        }
      }

      setState(() {});

      logger.i(
        'Loaded ${_models.length} models in ${stopwatch.elapsedMilliseconds} ms',
      );
    } catch (e) {
      logger.e('Failed to load models: $e');
    } finally {
      stopwatch.stop();
    }
  }

  Future<void> _sendMessage() async {
    if (_selectedModel != null && _messageController.text.isNotEmpty) {
      String message = _messageController.text;
      _messageController.clear();
      // Create and add user message to the conversation
      BotChatMessage userMessage = BotChatMessage(
        text: message,
        senderId: 'user',
        // Use appropriate user ID from Firebase Authentication
        timestamp: Timestamp.now(),
        isBot: false,
      );

      _conversation?.messages.add(userMessage);

      setState(() {});

      try {
        final stopwatch = Stopwatch()..start();
        // Use Flutter Gemma to get the response with chat history
        List<Message> messages = _conversation!.messages
            .map((msg) => Message(text: msg.text, isUser: !msg.isBot))
            .toList();

        // _chatMessages.add("${_selectedModel!.name}: ${response?? ""} ");
        // Stream<String> responseStream = engine.generateResponse(message);
        // Add initial response entry to chat
        _streamingResponse = ""; // Clear previous streaming response
        setState(() {}); // Update UI to show streaming box

        // Start streaming the response using .listen()
        FlutterGemmaPlugin.instance
            .getChatResponseAsync(messages: messages)
            .listen(
              (String? token) {
                if (token != null) {
                  _streamingResponse += token; // Accumulate response tokens
                  setState(() {}); // Update the UI with each token
                } else {
                  // When token is null, the stream is done
                  if (_streamingResponse.isNotEmpty) {
                    _streamingResponse = _streamingResponse.trim();
                    BotChatMessage botMessage = BotChatMessage(
                      text: _streamingResponse,
                      senderId: botId,
                      timestamp: Timestamp.now(),
                      isBot: true,
                    );
                    _conversation?.messages.add(botMessage);

                    _streamingResponse =
                        ""; // Clear the response after completion
                    setState(() {}); // Update the UI with the final bot message
                  }
                  logger.i('Streaming completed, final response added.');
                }
              },
              onError: (error) {
                logger.e('Error occurred during streaming: $error');
              },
            );

        logger.i('Response streaming started');
      } catch (e) {
        logger.e('Failed to get response from model: $e');
      } finally {
        setState(() {});
      }
    }
  }

  Future<String> getCacheDirectoryPath() async {
    final Directory tempDir = await getTemporaryDirectory();
    return tempDir.path; // Returns the path of the cache directory
  }

  String parsePath(String fullPath) {
    // Using regex to match the relevant part of the path
    final RegExp regex = RegExp(r'data/Containers/Data/Application/.*');
    final match = regex.firstMatch(fullPath);
    return match != null ? "/" + match.group(0)! : fullPath;
  }

  Future<String> getRelativePath(String absolutePath) async {
    // Get the application's documents directory
    final directory = await getApplicationDocumentsDirectory();

    // Get the documents directory path
    final documentsDirectoryPath = directory.path;

    // Check if the absolutePath starts with the documentsDirectoryPath
    if (absolutePath.startsWith(documentsDirectoryPath)) {
      // Remove the documents directory path from the absolute path
      final relativePath = absolutePath.replaceFirst(
        documentsDirectoryPath,
        '',
      );
      // Ensure the relative path starts from /Documents/
      return '/Documents$relativePath';
    } else {
      throw Exception(
        'The provided path does not belong to the documents directory.',
      );
    }
  }

  void _handleTranscription(String transcript) {
    logger.d('Transcribed text: $transcript');
    setState(() {
      _messageController.text = transcript;
    });
  }

  Future<void> _initializeGemma(Model model) async {
    setState(() {
      _isLoading = true; // Set loading to true
    });
    try {
      // Show a toast message indicating initialization has started
      BotToast.showText(text: "Initializing model, please wait...");
      logger.i('Initializing model: ${model.name}');

      // Check if the file exists
      final file = File(model.filePath);
      if (!await file.exists()) {
        logger.e('Model file does not exist at path: ${model.filePath}');
        // Show toast message to the user
        BotToast.showText(text: "Model file does not exist.");
        return; // Exit the function if the file does not exist
      }
      // now file already exists, we need to rename it to model.bin
      final directory =
          file.parent.path; // Get the directory of the current file
      final newPath =
          '$directory/model.bin'; // Construct the new path with the new name
      await file.rename(newPath); // Rename the file
      // Replace 'gemma-2b-it-cpu-int4.bin' with your actual file name
      final relativePath = await parsePath(model.filePath);

      await FlutterGemmaPlugin.instance.init(
        modelPath: newPath,
        // modelPath: newPath,
        maxTokens: 512,
        temperature: 1.0,
        topK: 1,
        randomSeed: 1,
      );
      logger.i("model initialize successful");
      BotToast.showText(text: "Model initialized successfully");
      //       String cache_dir = await getCacheDirectoryPath();
      //       bool isGpu = model.isgpu;
      //       final options = switch (isGpu) {
      //         true => LlmInferenceOptions.gpu(
      //             modelPath: model.filePath,
      //             sequenceBatchSize: 5,
      //             maxTokens: 512,
      //             temperature: 1.0,
      //             topK: 1,
      //             randomSeed: 1,
      //           ),
      //         false => LlmInferenceOptions.cpu(
      //             modelPath: model.filePath,
      //             cacheDir: cache_dir,
      //             maxTokens: 512,
      //             temperature: 1.0,
      //             topK: 1,
      //             randomSeed: 1,
      //           ),
      //       };
      //
      // // Create an inference engine
      //       engine = LlmInferenceEngine(options);
    } catch (e) {
      logger.e('Failed to initialize Gemma model: $e');
      BotToast.showText(text: "Failed to initialize model.");
    } finally {
      setState(() {
        _isLoading = false; // Set loading to false
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
      appBar: AppBarStyles.primary(
        title: 'Offline LLM Chat',
        actions: [
          IconButton(
            icon: Icon(Icons.info_outline, color: Colors.white),
            tooltip: 'Help & Tips',
            onPressed: () => _showHelpDialog(context),
          ),
          IconButton(
            icon: Icon(Icons.offline_bolt_outlined, color: Colors.white),
            tooltip: 'Download Models',
            onPressed: () async {
              bool isDesktop = AppPreferences.of(Get.context!).isDesktop;
              await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      isDesktop ? DesktopHomePage() : MobileHomePage(),
                ),
              );
              // Refresh models after coming back
              await _loadModels();
            },
          ),
          IconButton(
            icon: Icon(Icons.settings, color: Colors.white),
            tooltip: 'Model Management',
            onPressed: () async {
              await Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => ModelManagementPage()),
              );
              // Refresh models after coming back
              await _loadModels();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Enhanced model selection header
          _buildModelSelectionHeader(context, theme, colorScheme),

          // Enhanced chat messages list
          Expanded(child: _buildChatMessagesList(context, theme, colorScheme)),

          // Enhanced streaming response display
          if (_streamingResponse.isNotEmpty)
            _buildStreamingResponse(context, theme, colorScheme),

          // Enhanced message input field
          _buildMessageInput(context, theme, colorScheme),
        ],
      ),
    );
  }

  /// Builds the enhanced model selection header
  Widget _buildModelSelectionHeader(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return EnhancedCard(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.memory, color: AppTheme.primaryGreen, size: 24),
              const SizedBox(width: 8),
              Text(
                'AI Model Selection',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: colorScheme.outline),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      hint: Text(
                        'Select AI Model',
                        style: TextStyle(
                          color: colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                      value: _selectedModel,
                      isExpanded: true,
                      icon: Icon(
                        Icons.arrow_drop_down,
                        color: colorScheme.onSurface,
                      ),
                      onChanged: _onModelSelected,
                      items: _models.values.map<DropdownMenuItem<String>>((
                        Model model,
                      ) {
                        return DropdownMenuItem<String>(
                          value: model.name,
                          child: Row(
                            children: [
                              Icon(
                                model.isgpu ? Icons.speed : Icons.memory,
                                size: 16,
                                color: model.isgpu
                                    ? Colors.orange
                                    : Colors.blue,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  model.name,
                                  style: TextStyle(
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              if (_isLoading)
                LoadingStyles.primary(size: 24, message: 'Initializing...'),
            ],
          ),
          if (_selectedModel != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: AppTheme.primaryGreen,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Model ready for conversation',
                    style: TextStyle(
                      color: AppTheme.primaryGreen,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Builds the enhanced chat messages list
  Widget _buildChatMessagesList(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    if (_conversation?.messages.isEmpty ?? true) {
      return _buildEmptyState(context, theme, colorScheme);
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: _conversation?.messages.length ?? 0,
      itemBuilder: (context, index) {
        final message = _conversation!.messages[index];
        final isUserMessage = !message.isBot;

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            mainAxisAlignment: isUserMessage
                ? MainAxisAlignment.end
                : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!isUserMessage) ...[
                _buildBotAvatar(),
                const SizedBox(width: 8),
              ],
              Flexible(
                child: _buildMessageBubble(
                  message,
                  isUserMessage,
                  theme,
                  colorScheme,
                ),
              ),
              if (isUserMessage) ...[
                const SizedBox(width: 8),
                _buildUserAvatar(),
              ],
            ],
          ),
        );
      },
    );
  }

  /// Builds empty state when no messages
  Widget _buildEmptyState(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'Start a conversation',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select a model and type your first message',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Builds user avatar
  Widget _buildUserAvatar() {
    // TODO: Replace with actual user profile data following user_profile_page standard
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppTheme.primaryGreen,
        border: Border.all(color: Colors.white, width: 2),
      ),
      child: Icon(Icons.person, color: Colors.white, size: 18),
    );
  }

  /// Builds bot avatar
  Widget _buildBotAvatar() {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppTheme.accentBlue,
        border: Border.all(color: Colors.white, width: 2),
      ),
      child: Icon(Icons.smart_toy, color: Colors.white, size: 18),
    );
  }

  /// Builds individual message bubble
  Widget _buildMessageBubble(
    BotChatMessage message,
    bool isUserMessage,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return EnhancedCard(
      margin: EdgeInsets.zero,
      padding: const EdgeInsets.all(12),
      backgroundColor: isUserMessage
          ? AppTheme.primaryGreen
          : colorScheme.surface,
      border: isUserMessage
          ? null
          : Border.all(color: colorScheme.outline.withValues(alpha: 0.3)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            message.text,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isUserMessage ? Colors.white : colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                DateFormat.jm().format(message.timestamp.toDate()),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isUserMessage
                      ? Colors.white.withValues(alpha: 0.7)
                      : colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              if (!isUserMessage) ...[
                const SizedBox(width: 8),
                _buildMessageActions(message, colorScheme),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// Builds message action buttons
  Widget _buildMessageActions(BotChatMessage message, ColorScheme colorScheme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: Icon(Icons.volume_up, size: 16),
          color: colorScheme.onSurface.withValues(alpha: 0.6),
          onPressed: () {
            // TODO: Implement TTS functionality
          },
          tooltip: 'Read aloud',
        ),
        IconButton(
          icon: Icon(Icons.copy, size: 16),
          color: colorScheme.onSurface.withValues(alpha: 0.6),
          onPressed: () {
            Clipboard.setData(ClipboardData(text: message.text));
            BotToast.showText(text: 'Copied to clipboard');
          },
          tooltip: 'Copy message',
        ),
        IconButton(
          icon: Icon(Icons.share, size: 16),
          color: colorScheme.onSurface.withValues(alpha: 0.6),
          onPressed: () {
            // TODO: Implement share functionality
          },
          tooltip: 'Share message',
        ),
      ],
    );
  }

  /// Builds streaming response display
  Widget _buildStreamingResponse(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBotAvatar(),
          const SizedBox(width: 8),
          Flexible(
            child: EnhancedCard(
              margin: EdgeInsets.zero,
              padding: const EdgeInsets.all(12),
              backgroundColor: AppTheme.accentBlue.withValues(alpha: 0.1),
              border: Border.all(
                color: AppTheme.accentBlue.withValues(alpha: 0.3),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      LoadingStyles.dots(size: 16, color: AppTheme.accentBlue),
                      const SizedBox(width: 8),
                      Text(
                        'AI is typing...',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: AppTheme.accentBlue,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _streamingResponse,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds enhanced message input field
  Widget _buildMessageInput(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(color: colorScheme.outline.withValues(alpha: 0.3)),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Voice input button
            if (kIsWeb || (!kIsWeb && !(Platform.isMacOS && kDebugMode)))
              Container(
                margin: const EdgeInsets.only(right: 8),
                child: IconButton(
                  icon: Icon(Icons.mic, color: AppTheme.primaryGreen),
                  onPressed: () {
                    // TODO: Implement voice input functionality
                  },
                  tooltip: 'Voice input',
                ),
              ),

            // Text input field
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: colorScheme.outline),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: TextField(
                  controller: _messageController,
                  maxLines: null,
                  textCapitalization: TextCapitalization.sentences,
                  decoration: InputDecoration(
                    hintText: _selectedModel != null
                        ? 'Type your message...'
                        : 'Select a model first',
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    hintStyle: TextStyle(
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                  enabled: _selectedModel != null && !_isLoading,
                  onSubmitted: (_) => _sendMessage(),
                ),
              ),
            ),

            const SizedBox(width: 8),

            // Send button
            Container(
              decoration: BoxDecoration(
                color: _canSendMessage()
                    ? AppTheme.primaryGreen
                    : colorScheme.outline,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: Icon(Icons.send, color: Colors.white),
                onPressed: _canSendMessage() ? _sendMessage : null,
                tooltip: 'Send message',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Checks if message can be sent
  bool _canSendMessage() {
    return _selectedModel != null &&
        !_isLoading &&
        _messageController.text.trim().isNotEmpty;
  }

  /// Shows help dialog with tips and instructions for offline LLM chat
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.help_outline, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('Offline LLM Chat Help'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHelpSection('Getting Started', [
                'Download and select a local model first',
                'Wait for model initialization to complete',
                'Start chatting with your offline AI assistant',
                'Use voice input for hands-free interaction',
              ]),
              SizedBox(height: 16),
              _buildHelpSection('Features', [
                'Completely offline - no internet required',
                'Voice input and text-to-speech support',
                'Copy and share responses',
                'Conversation history within session',
                'Multiple model support',
              ]),
              SizedBox(height: 16),
              _buildHelpSection('Tips for Better Conversations', [
                'Be clear and specific in your questions',
                'Break complex topics into smaller parts',
                'Use follow-up questions for clarification',
                'Experiment with different models for varied responses',
              ]),
              SizedBox(height: 16),
              _buildHelpSection('Model Management', [
                'Download models via the download button',
                'Manage models in the settings menu',
                'Larger models generally provide better responses',
                'Models are stored locally on your device',
              ]),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Got it!'),
          ),
        ],
      ),
    );
  }

  /// Builds help section with title and tips
  Widget _buildHelpSection(String title, List<String> tips) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        SizedBox(height: 8),
        ...tips.map(
          (tip) => Padding(
            padding: EdgeInsets.symmetric(vertical: 2),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  size: 16,
                  color: AppTheme.secondaryGreen,
                ),
                SizedBox(width: 8),
                Expanded(child: Text(tip)),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
