import 'package:flutter/material.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/app_preferences.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/artificial_intelligence.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/characters.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/huggingface_selection.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/sessions.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/user.dart';
import 'package:diogeneschatbot/models/maid_properties.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/desktop/app.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/mobile/app.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:provider/provider.dart';

class MaidApp extends StatefulWidget {
  const MaidApp({super.key});

  @override
  _MaidAppState createState() => _MaidAppState();
}

class _MaidAppState extends State<MaidApp> {
  MaidProperties? props;

  @override
  void initState() {
    super.initState();
    // Load MaidProperties after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      props = await MaidProperties.last;
      // After loading, call setState to rebuild the widget
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    // Show enhanced loading indicator if props is null
    if (props == null) {
      return Scaffold(
        backgroundColor: AppTheme.isDarkMode(context)
            ? AppTheme.darkSurface
            : AppTheme.lightSurface,
        body: Center(
          child: LoadingStyles.primary(
            size: 60,
            message: 'Initializing Maid AI...',
          ),
        ),
      );
    }
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => props!.appPreferences,
          key: ValueKey('appPreferencesProvider_maid'), // Adding unique key
        ),
        ChangeNotifierProvider(
          create: (context) => props!.sessions,
          key: ValueKey('sessionsProvider_maid'), // Adding unique key
        ),
        ChangeNotifierProvider(
          create: (context) => props!.characters,
          key: ValueKey('charactersProvider_maid'), // Adding unique key
        ),
        ChangeNotifierProvider(
          create: (context) => props!.ai,
          key: ValueKey('aiProvider_maid'), // Adding unique key
        ),
        ChangeNotifierProvider(
          create: (context) => props!.user,
          key: ValueKey('userProvider_maid'), // Adding unique key
        ),
        ChangeNotifierProvider(
          create: (context) => HuggingfaceSelection(),
          key: ValueKey(
            'huggingfaceSelectionProvider_maid',
          ), // Adding unique key
        ),
      ],
      child: Selector<AppPreferences, bool>(
        selector: (context, appPreferences) => appPreferences.isDesktop,
        builder: (context, isDesktop, child) {
          if (isDesktop) {
            return const DesktopApp();
          } else {
            return const MobileApp();
          }
        },
      ),
    );
  }
}
