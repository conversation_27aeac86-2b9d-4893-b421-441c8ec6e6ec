import 'dart:convert';
import 'dart:io';

import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/models/offline_model.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:diogeneschatbot/widgets/enhanced_button.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_download_manager/flutter_download_manager.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:step_progress_indicator/step_progress_indicator.dart';
import 'package:bot_toast/bot_toast.dart';

class ModelManagementPage extends StatefulWidget {
  @override
  _ModelManagementPageState createState() => _ModelManagementPageState();
}

class _ModelManagementPageState extends State<ModelManagementPage> {
  final DownloadManager _downloadManager = DownloadManager();
  Map<String, Model> _models = {};
  bool _loading = true;
  String _savedDir = '';

  // final storage = FlutterSecureStorage();

  @override
  void initState() {
    super.initState();
    _fetchModels();
    _initializeDirectory();
  }

  static String offline_models_key = "offline_models";

  Future<void> _saveModelsToLocal() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    // Convert the models map to a list and encode to JSON
    List<String> modelJsonList = _models.values
        .map((model) => jsonEncode(model.toJson()))
        .toList();

    // Save the list as a single string
    await prefs.setString(offline_models_key, jsonEncode(modelJsonList));
  }

  Future<void> _loadModelsFromLocal() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    try {
      // Get the list of model JSON strings
      String? modelJsonString = prefs.getString(offline_models_key);
      if (modelJsonString != null) {
        // Decode the list of model JSON strings
        List<dynamic> modelJsonList = jsonDecode(modelJsonString);
        for (var json in modelJsonList) {
          Model loadedModel = Model.fromJson(jsonDecode(json));
          // Update existing model with loaded data
          if (_models.containsKey(loadedModel.name)) {
            _models[loadedModel.name]!.isDownloaded = loadedModel.isDownloaded;
            _models[loadedModel.name]!.downloadedSize =
                loadedModel.downloadedSize;
            _models[loadedModel.name]!.progress = loadedModel.progress;
            _models[loadedModel.name]!.lastReportedProgress =
                loadedModel.lastReportedProgress;
            _models[loadedModel.name]!.totalSize = loadedModel.totalSize;
            _models[loadedModel.name]!.filePath = loadedModel.filePath;
            _models[loadedModel.name]!.downloadStatus =
                loadedModel.downloadStatus;
            _models[loadedModel.name]!.statusMessage =
                loadedModel.statusMessage;
            _models[loadedModel.name]!.downloadTime = loadedModel.downloadTime;
          } else {
            // If the model does not exist, add it to the models map
            _models[loadedModel.name] = loadedModel;
          }
        }
      }
      setState(() {});
    } catch (e) {
      logger.e('Error loading models from local: $e');
    }
  }

  Future<void> _initializeDirectory() async {
    Directory dir;

    // Check the platform
    if (Platform.isAndroid || Platform.isWindows) {
      // Use the application support directory for mobile and desktop platforms
      dir = await getApplicationSupportDirectory();
    } else if (Platform.isIOS || Platform.isMacOS) {
      dir = await getApplicationDocumentsDirectory();
    } else {
      // Fallback option for unsupported platforms (if necessary)
      dir =
          await getTemporaryDirectory(); // Use temporary directory as a fallback
    }
    logger.i("Local directory path: ${dir.path}");

    setState(() {
      _savedDir = dir.path;
    });
  }

  Future<void> _fetchModels() async {
    // Prepare an empty list to hold the models
    Map<String, Model> models = {};

    try {
      // Fetch download URLs and sizes for each model
      Model? model = null;
      // var gemmaCpuInt4 = await getDownloadUrl(
      //     'public/models/google/gemma/tfLite/gemma-2b-it-cpu-int4/gemma-2b-it-cpu-int4.bin');
      // model = Model(
      //   name: 'gemma-2b-it-cpu-int4',
      //   url: gemmaCpuInt4['url'],
      //   size: gemmaCpuInt4['size'],
      //   // Use the size returned from getDownloadUrl
      //   description: 'gemma-2b-it-cpu-int4',
      //   isgpu: false,
      // );
      // models[model.name]=model;

      var gemmaGpuInt4 = await getDownloadUrl(
        'public/models/google/gemma/tfLite/gemma-2b-it-gpu-int4/gemma-2b-it-gpu-int4.bin',
      );
      model = Model(
        name: 'gemma-2b-it-gpu-int4',
        url: gemmaGpuInt4['url'],
        size: gemmaGpuInt4['size'],
        // Use the size returned from getDownloadUrl
        description: 'gemma-2b-it-gpu-int4',
        isgpu: true,
      );
      models[model.name] = model;

      // var gemmaCpuInt8 = await getDownloadUrl(
      //     'public/models/google/gemma2/tfLite/gemma2-2b-it-cpu-int8/gemma2-2b-it-cpu-int8.bin');
      // model = Model(
      //   name: 'gemma2-2b-it-cpu-int8',
      //   url: gemmaCpuInt8['url'],
      //   size: gemmaCpuInt8['size'],
      //   // Use the size returned from getDownloadUrl
      //   description: 'gemma2-2b-it-cpu-int8',
      //   isgpu: false,
      // );
      // models[model.name]=model;
      //
      // var gemmaGpuInt8 = await getDownloadUrl(
      //     'public/models/google/gemma2/tfLite/gemma2-2b-it-gpu-int8/gemma2-2b-it-gpu-int8.bin');
      // model = Model(
      //   name: 'gemma2-2b-it-gpu-int8',
      //   url: gemmaGpuInt8['url'],
      //   size: gemmaGpuInt8['size'],
      //   // Use the size returned from getDownloadUrl
      //   description: 'gemma2-2b-it-gpu-int8',
      //   isgpu: true,
      // );
      // models[model.name]=model;
    } catch (e) {
      logger.i('Error fetching download URLs: $e');
      // Handle errors as needed (e.g., show a message to the user)
    }

    // Call setState once with the new models
    setState(() {
      _models = models;
      _loading = false;
    });

    _loadModelsFromLocal(); // Load models from local storage
  }

  Future<Map<String, dynamic>> getDownloadUrl(String path) async {
    try {
      final ref = FirebaseStorage.instance.ref(path);

      // Get the download URL
      String downloadUrl = await ref.getDownloadURL();

      // Get the file size
      final fullMetadata = await ref.getMetadata();
      int fileSize = fullMetadata.size!; // Size in bytes

      return {
        'url': downloadUrl,
        'size': fileSize, // Return the size
      };
    } catch (e) {
      logger.i('Error getting download URL: $e');
      return {'url': '', 'size': 0}; // Return zero size on error
    }
  }

  Future<void> _downloadModel(Model model) async {
    try {
      final uri = Uri.parse(model.url);
      final fileName = p.basename(
        uri.pathSegments.last,
      ); // Use basename to get only the filename
      final filePath = "$_savedDir/$fileName";

      model.filePath = filePath;
      final task = await _downloadManager.addDownload(model.url, filePath);

      int lastDownloadedBytes =
          0; // Track bytes downloaded for speed calculation
      DateTime lastCheckTime =
          DateTime.now(); // Track time for speed calculation

      task?.status.addListener(() {
        setState(() {
          model.downloadStatus = task.status.value;
          model.isDownloaded = task.status.value == DownloadStatus.completed;
        });
        logger.i('Download status: ${task.status.value}');
      });

      task?.progress.addListener(() {
        // Check if progress has changed significantly
        if ((task.progress.value - model.lastReportedProgress).abs() >= 0.01) {
          // Update the last reported progress and the current progress
          model.lastReportedProgress = task.progress.value;
          model.downloadStatus = task.status.value;
          model.progress = model.lastReportedProgress; // Update the progress

          // Log only when progress increases by a significant amount (e.g., 5%)
          if ((model.progress * 100).toInt() % 5 == 0) {
            logger.i('Download progress: ${task.progress.value}');
          }

          // Call setState only when significant progress change is detected
          setState(() {
            // Calculate download speed
            int currentDownloadedBytes = (model.progress * model.size)
                .toInt(); // Use the known size
            Duration duration = DateTime.now().difference(lastCheckTime);

            if (duration.inSeconds > 0) {
              int downloadedSinceLastCheck =
                  currentDownloadedBytes - lastDownloadedBytes;
              double speed =
                  downloadedSinceLastCheck / duration.inSeconds; // KB/sec

              if (speed < 50) {
                // Adjust threshold as needed (e.g., 50 KB/sec)
                model.statusMessage =
                    'Download is slow, current speed: ${speed.toStringAsFixed(2)} KB/s';
              } else {
                model.statusMessage = '';
              }

              lastDownloadedBytes =
                  currentDownloadedBytes; // Update for next check
              model.downloadedSize = lastDownloadedBytes;
              lastCheckTime = DateTime.now(); // Reset check time
            }
          });
        }
        ;
      });

      await task?.whenDownloadComplete();
      model.progress = 1;
      model.downloadTime =
          DateTime.now(); // Set the download time when download completes
      await _saveModelsToLocal(); // Save model details to local storage after download completes
      logger.i('Download completed for ${model.name}.');
      setState(() {});
    } catch (e) {
      logger.e('Error downloading model ${model.name}: $e');
      setState(() {
        model.downloadStatus = DownloadStatus.failed;
      });
    }
  }

  Future<void> _deleteModel(Model model) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    try {
      final filePath = model.filePath;
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        logger.i('Deleted file: $filePath');
      }
      // // Remove the model from the _models map
      // _models.remove(model.name);
      //
      // // Save the updated list of models to SharedPreferences
      // await _saveModelsToLocal(); // Save the remaining models after deletion

      setState(() {
        model.isDownloaded = false;
        model.downloadedSize = 0;
        model.totalSize = 0;
        model.filePath = ''; // Clear the saved path
        model.downloadStatus = DownloadStatus.queued; // Reset download status
        model.progress = 0; // Reset progress
        model.lastReportedProgress = 0.0; // Reset last reported progress
        model.statusMessage = ''; // Clear status message
        model.downloadTime = null; // Optionally reset download time
      });

      // Remove the model from SharedPreferences
      final modelJsonString = prefs.getString(offline_models_key);
      if (modelJsonString != null) {
        List<dynamic> modelJsonList = jsonDecode(modelJsonString);
        // Find the index of the model to update
        final indexToUpdate = modelJsonList.indexWhere((json) {
          Model loadedModel = Model.fromJson(jsonDecode(json));
          return loadedModel.name == model.name; // Match the model to update
        });

        // If the model exists in the list, update it
        if (indexToUpdate != -1) {
          modelJsonList[indexToUpdate] = jsonEncode(
            model.toJson(),
          ); // Update with the modified model
        }

        // Save the updated list back to SharedPreferences
        await prefs.setString(offline_models_key, jsonEncode(modelJsonList));
      }
    } catch (e) {
      logger.e('Error deleting model ${model.name}: $e');
    }
  }

  Future<void> _pauseDownload(Model model) async {
    final task = _downloadManager.getDownload(model.url);
    if (task != null) {
      _downloadManager.pauseDownload(model.url);
    }
  }

  Future<void> _resumeDownload(Model model) async {
    final task = _downloadManager.getDownload(model.url);
    if (task != null) {
      _downloadManager.resumeDownload(model.url);
    }
  }

  Future<void> _cancelDownload(Model model) async {
    final task = _downloadManager.getDownload(model.url);
    if (task != null) {
      _downloadManager.cancelDownload(model.url);
      _deleteModel(model);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
      appBar: AppBarStyles.primary(
        title: 'Model Management',
        onBackPressed: () => Navigator.of(context).pop(),
      ),
      body: _loading
          ? _buildLoadingState()
          : _models.isEmpty
          ? _buildEmptyState(context, theme, colorScheme)
          : Column(
              children: [
                Expanded(
                  // This allows the Column to expand and fill available space
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _models.length,
                    itemBuilder: (context, index) {
                      final model = _models.values.elementAt(index);
                      return _buildModelCard(
                        model,
                        context,
                        theme,
                        colorScheme,
                      );
                    },
                  ),
                ),
              ],
            ),
    );
  }

  /// Builds loading state
  Widget _buildLoadingState() {
    return Center(
      child: LoadingStyles.primary(size: 60, message: 'Loading models...'),
    );
  }

  /// Builds empty state when no models available
  Widget _buildEmptyState(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_download_outlined,
              size: 80,
              color: colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 24),
            Text(
              'No Models Available',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Models will appear here when they become available for download.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ButtonStyles.primary(
              text: 'Refresh',
              onPressed: _fetchModels,
              icon: Icons.refresh,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds enhanced model card
  Widget _buildModelCard(
    Model model,
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    final isDownloaded = model.isDownloaded;
    final isDownloading = model.downloadStatus == DownloadStatus.downloading;
    final isFailed = model.downloadStatus == DownloadStatus.failed;

    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      backgroundColor: isDownloaded
          ? AppTheme.primaryGreen.withValues(alpha: 0.1)
          : isFailed
          ? Colors.red.withValues(alpha: 0.1)
          : null,
      border: isDownloaded
          ? Border.all(color: AppTheme.primaryGreen.withValues(alpha: 0.3))
          : isFailed
          ? Border.all(color: Colors.red.withValues(alpha: 0.3))
          : null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Model header
          Row(
            children: [
              Icon(
                model.isgpu ? Icons.speed : Icons.memory,
                color: model.isgpu ? Colors.orange : Colors.blue,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      model.name,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    Text(
                      model.description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              _buildStatusChip(model, colorScheme),
            ],
          ),

          const SizedBox(height: 16),

          // Model details
          _buildModelDetails(model, theme, colorScheme),

          if (isDownloading || model.progress > 0) ...[
            const SizedBox(height: 16),
            _buildProgressSection(model, theme, colorScheme),
          ],

          const SizedBox(height: 16),

          // Action buttons
          _buildActionButtons(model, colorScheme),
        ],
      ),
    );
  }

  /// Builds status chip for model
  Widget _buildStatusChip(Model model, ColorScheme colorScheme) {
    String text;
    Color color;
    IconData icon;

    if (model.isDownloaded) {
      text = 'Downloaded';
      color = AppTheme.primaryGreen;
      icon = Icons.check_circle;
    } else if (model.downloadStatus == DownloadStatus.downloading) {
      text = 'Downloading';
      color = Colors.blue;
      icon = Icons.download;
    } else if (model.downloadStatus == DownloadStatus.failed) {
      text = 'Failed';
      color = Colors.red;
      icon = Icons.error;
    } else if (model.downloadStatus == DownloadStatus.paused) {
      text = 'Paused';
      color = Colors.orange;
      icon = Icons.pause;
    } else {
      text = 'Available';
      color = colorScheme.onSurface.withValues(alpha: 0.6);
      icon = Icons.cloud_download;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds model details section
  Widget _buildModelDetails(
    Model model,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Row(
      children: [
        Expanded(
          child: _buildDetailItem(
            'Size',
            '${(model.size / (1024 * 1024)).toStringAsFixed(1)} MB',
            Icons.storage,
            theme,
            colorScheme,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildDetailItem(
            'Type',
            model.isgpu ? 'GPU' : 'CPU',
            model.isgpu ? Icons.speed : Icons.memory,
            theme,
            colorScheme,
          ),
        ),
        if (model.isDownloaded) ...[
          const SizedBox(width: 16),
          Expanded(
            child: _buildDetailItem(
              'Downloaded',
              '${(model.downloadedSize / (1024 * 1024)).toStringAsFixed(1)} MB',
              Icons.download_done,
              theme,
              colorScheme,
            ),
          ),
        ],
      ],
    );
  }

  /// Builds individual detail item
  Widget _buildDetailItem(
    String label,
    String value,
    IconData icon,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16,
                color: colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds progress section for downloading models
  Widget _buildProgressSection(
    Model model,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    final progress = model.progress;
    final progressPercent = (progress * 100).toInt();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Download Progress',
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            Text(
              '$progressPercent%',
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
                color: colorScheme.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress.toDouble(),
          backgroundColor: colorScheme.outline.withValues(alpha: 0.3),
          valueColor: AlwaysStoppedAnimation<Color>(
            model.downloadStatus == DownloadStatus.failed
                ? Colors.red
                : AppTheme.primaryGreen,
          ),
        ),
        if (model.statusMessage.isNotEmpty) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.warning, size: 16, color: Colors.orange),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    model.statusMessage,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.orange.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Builds action buttons for model management
  Widget _buildActionButtons(Model model, ColorScheme colorScheme) {
    return Row(
      children: [
        if (model.downloadStatus == DownloadStatus.queued ||
            model.downloadStatus == DownloadStatus.failed ||
            model.downloadStatus == DownloadStatus.canceled) ...[
          Expanded(
            child: ButtonStyles.primary(
              text: 'Download',
              onPressed: () => _downloadModel(model),
              icon: Icons.download,
              size: ButtonSize.small,
            ),
          ),
        ],

        if (model.downloadStatus == DownloadStatus.downloading) ...[
          Expanded(
            child: ButtonStyles.secondary(
              text: 'Pause',
              onPressed: () => _pauseDownload(model),
              icon: Icons.pause,
              size: ButtonSize.small,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: ButtonStyles.secondary(
              text: 'Cancel',
              onPressed: () => _cancelDownload(model),
              icon: Icons.cancel,
              size: ButtonSize.small,
            ),
          ),
        ],

        if (model.downloadStatus == DownloadStatus.paused) ...[
          Expanded(
            child: ButtonStyles.primary(
              text: 'Resume',
              onPressed: () => _resumeDownload(model),
              icon: Icons.play_arrow,
              size: ButtonSize.small,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: ButtonStyles.secondary(
              text: 'Cancel',
              onPressed: () => _cancelDownload(model),
              icon: Icons.cancel,
              size: ButtonSize.small,
            ),
          ),
        ],

        if (model.isDownloaded ||
            model.downloadStatus == DownloadStatus.failed) ...[
          if (!model.isDownloaded)
            Expanded(
              child: ButtonStyles.primary(
                text: 'Retry',
                onPressed: () => _downloadModel(model),
                icon: Icons.refresh,
                size: ButtonSize.small,
              ),
            ),
          if (model.isDownloaded) const Spacer(),
          const SizedBox(width: 8),
          ButtonStyles.secondary(
            text: 'Delete',
            onPressed: () => _showDeleteConfirmation(model),
            icon: Icons.delete,
            size: ButtonSize.small,
          ),
        ],
      ],
    );
  }

  /// Shows delete confirmation dialog
  void _showDeleteConfirmation(Model model) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Model'),
        content: Text(
          'Are you sure you want to delete "${model.name}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteModel(model);
              BotToast.showText(text: 'Model deleted successfully');
            },
            child: Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
