import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/mobile/buttons/menu_button.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/shared/dropdowns/llm_platform_dropdown.dart';
import 'package:diogeneschatbot/pages/home_page.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:flutter/material.dart';

class HomeAppBar extends StatelessWidget implements PreferredSizeWidget {
  const HomeAppBar({super.key});

  @override
  Size get preferredSize => const Size.fromHeight(56.0);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.primaryGreen, AppTheme.accentBlue],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: AppBar(
        elevation: 0.0,
        backgroundColor: Colors.transparent,
        title: const LlmPlatformDropdown(),
        centerTitle: false,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          tooltip: 'Back to Main App',
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const HomePage(title: 'Home'),
              ),
            );
          },
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: IconButton(
              icon: const Icon(Icons.add_circle_outline, color: Colors.white),
              tooltip: 'New Session',
              onPressed: () {
                // TODO: Implement new session functionality
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: IconButton(
              icon: const Icon(Icons.settings, color: Colors.white),
              tooltip: 'Settings',
              onPressed: () {
                // TODO: Implement settings functionality
              },
            ),
          ),
          const Padding(
            padding: EdgeInsets.only(right: 8.0),
            child: MenuButton(),
          ),
        ],
      ),
    );
  }
}
