import 'dart:math';

import 'package:flutter/material.dart';
import 'package:diogeneschatbot/models/chat_node.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/characters.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/sessions.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/user.dart';
import 'package:diogeneschatbot/models/enums/chat_role.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/static/utilities.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/shared/chat_widgets/chat_field.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/shared/chat_widgets/chat_message.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:provider/provider.dart';

class ChatBody extends StatelessWidget {
  const ChatBody({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
            isDark
                ? AppTheme.darkSurface.withValues(alpha: 0.8)
                : AppTheme.lightSurface.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        children: [
          Expanded(child: _buildChat(context, theme, colorScheme)),
          const ChatField(),
        ],
      ),
    );
  }

  List<ChatMessageWidget> _getChatWidgets(List<ChatNode> chat) {
    List<ChatMessageWidget> chatWidgets = [];

    for (final message in chat) {
      chatWidgets.add(ChatMessageWidget(hash: message.hash));
    }

    return chatWidgets;
  }

  Widget _buildChat(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Consumer3<Sessions, CharacterCollection, User>(
      builder: (context, sessions, characters, user, child) {
        List<ChatNode> chat = sessions.current.chat.getChat();

        if (chat.isEmpty &&
            characters.current.useGreeting &&
            characters.current.greetings.isNotEmpty) {
          final index = Random().nextInt(characters.current.greetings.length);

          if (characters.current.greetings[index].isNotEmpty) {
            final message = ChatNode(
              role: ChatRole.assistant,
              content: Utilities.formatPlaceholders(
                characters.current.greetings[index],
                user.name,
                characters.current.name,
              ),
              finalised: true,
            );

            sessions.current.chat.addNode(message);
            chat = [message];
          }
        }

        final chatWidgets = _getChatWidgets(chat);

        // Show enhanced empty state if no messages
        if (chatWidgets.isEmpty) {
          return _buildEmptyState(
            context,
            theme,
            colorScheme,
            characters.current.name,
          );
        }

        return Builder(
          builder: (BuildContext context) => GestureDetector(
            onHorizontalDragEnd: (details) {
              // Check if the drag is towards right with a certain velocity
              if (details.primaryVelocity! > 100) {
                // Open the drawer
                Scaffold.of(context).openDrawer();
              }
            },
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              itemCount: chatWidgets.length,
              itemBuilder: (BuildContext context, int index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: chatWidgets[index],
                );
              },
            ),
          ),
        );
      },
    );
  }

  /// Builds enhanced empty state
  Widget _buildEmptyState(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
    String characterName,
  ) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.primaryGreen.withValues(alpha: 0.1),
                    AppTheme.accentBlue.withValues(alpha: 0.1),
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.chat_bubble_outline,
                size: 64,
                color: AppTheme.primaryGreen,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Start chatting with $characterName',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'Type a message below to begin your conversation',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: AppTheme.primaryGreen.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.lightbulb_outline,
                    color: AppTheme.primaryGreen,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Tip: Swipe right to open the menu',
                    style: TextStyle(
                      color: AppTheme.primaryGreen,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
