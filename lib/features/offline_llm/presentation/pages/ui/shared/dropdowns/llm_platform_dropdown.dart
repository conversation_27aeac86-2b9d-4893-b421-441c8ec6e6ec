import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/artificial_intelligence.dart';
import 'package:diogeneschatbot/models/enums/large_language_model_type.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:provider/provider.dart';

class LlmPlatformDropdown extends StatefulWidget {
  const LlmPlatformDropdown({super.key});

  @override
  State<LlmPlatformDropdown> createState() => _LlmPlatformDropdownState();
}

class _LlmPlatformDropdownState extends State<LlmPlatformDropdown> {
  bool open = false;
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Consumer<ArtificialIntelligence>(
      builder: (context, ai, child) {
        return _buildEnhancedDropdown(context, ai, theme, colorScheme);
      },
    );
  }

  Widget _buildEnhancedDropdown(
    BuildContext context,
    ArtificialIntelligence ai,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryGreen.withValues(alpha: 0.8),
            AppTheme.accentBlue.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _showModelSelectionBottomSheet(context, ai),
        borderRadius: BorderRadius.circular(16),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _getModelIcon(ai.llm.type),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  ai.llm.type.displayName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _getModelDescription(ai.llm.type),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(width: 8),
            if (isLoading)
              LoadingStyles.dots(size: 16, color: Colors.white)
            else
              Icon(
                open ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                color: Colors.white,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }

  /// Shows enhanced model selection bottom sheet
  void _showModelSelectionBottomSheet(
    BuildContext context,
    ArtificialIntelligence ai,
  ) {
    setState(() => open = true);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildModelSelectionSheet(context, ai),
    ).then((_) {
      setState(() => open = false);
    });
  }

  /// Builds the enhanced model selection sheet
  Widget _buildModelSelectionSheet(
    BuildContext context,
    ArtificialIntelligence ai,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppTheme.primaryGreen, AppTheme.accentBlue],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.psychology, color: Colors.white, size: 28),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Select AI Model',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
          ),

          // Model options
          Flexible(
            child: ListView(
              shrinkWrap: true,
              padding: const EdgeInsets.all(16),
              children: _buildModelOptions(context, ai, theme, colorScheme),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds model option cards
  List<Widget> _buildModelOptions(
    BuildContext context,
    ArtificialIntelligence ai,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    final models = [
      LargeLanguageModelType.llamacpp,
      LargeLanguageModelType.anthropic,
      LargeLanguageModelType.openAI,
      LargeLanguageModelType.ollama,
      LargeLanguageModelType.mistralAI,
      LargeLanguageModelType.gemini,
    ];

    return models
        .map(
          (model) =>
              _buildModelOptionCard(context, ai, model, theme, colorScheme),
        )
        .toList();
  }

  /// Builds individual model option card
  Widget _buildModelOptionCard(
    BuildContext context,
    ArtificialIntelligence ai,
    LargeLanguageModelType model,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    final isSelected = ai.llm.type == model;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: EnhancedCard(
        margin: EdgeInsets.zero,
        padding: const EdgeInsets.all(16),
        backgroundColor: isSelected
            ? AppTheme.primaryGreen.withValues(alpha: 0.1)
            : null,
        border: isSelected
            ? Border.all(color: AppTheme.primaryGreen, width: 2)
            : null,
        child: InkWell(
          onTap: () {
            Navigator.pop(context);
            switchModel(model);
          },
          borderRadius: BorderRadius.circular(12),
          child: Row(
            children: [
              _getModelIcon(model),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      model.displayName,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isSelected
                            ? AppTheme.primaryGreen
                            : colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getModelDescription(model),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildModelFeatures(model, theme, colorScheme),
                  ],
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: AppTheme.primaryGreen,
                  size: 24,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Gets icon for model type
  Widget _getModelIcon(LargeLanguageModelType model) {
    IconData iconData;
    Color color;

    switch (model) {
      case LargeLanguageModelType.llamacpp:
        iconData = Icons.memory;
        color = Colors.orange;
        break;
      case LargeLanguageModelType.anthropic:
        iconData = Icons.psychology;
        color = Colors.purple;
        break;
      case LargeLanguageModelType.openAI:
        iconData = Icons.auto_awesome;
        color = Colors.green;
        break;
      case LargeLanguageModelType.ollama:
        iconData = Icons.computer;
        color = Colors.blue;
        break;
      case LargeLanguageModelType.mistralAI:
        iconData = Icons.wind_power;
        color = Colors.red;
        break;
      case LargeLanguageModelType.gemini:
        iconData = Icons.diamond;
        color = Colors.cyan;
        break;
      case LargeLanguageModelType.none:
        iconData = Icons.help_outline;
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Icon(iconData, color: color, size: 24),
    );
  }

  /// Gets description for model type
  String _getModelDescription(LargeLanguageModelType model) {
    switch (model) {
      case LargeLanguageModelType.llamacpp:
        return 'Local CPU/GPU inference';
      case LargeLanguageModelType.anthropic:
        return 'Claude AI by Anthropic';
      case LargeLanguageModelType.openAI:
        return 'GPT models by OpenAI';
      case LargeLanguageModelType.ollama:
        return 'Local Ollama server';
      case LargeLanguageModelType.mistralAI:
        return 'Mistral AI models';
      case LargeLanguageModelType.gemini:
        return 'Google Gemini AI';
      case LargeLanguageModelType.none:
        return 'No model selected';
    }
  }

  /// Builds model features chips
  Widget _buildModelFeatures(
    LargeLanguageModelType model,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    List<String> features;

    switch (model) {
      case LargeLanguageModelType.llamacpp:
        features = ['Offline', 'Privacy', 'GPU Support'];
        break;
      case LargeLanguageModelType.anthropic:
        features = ['Advanced', 'Reasoning', 'Safe'];
        break;
      case LargeLanguageModelType.openAI:
        features = ['Popular', 'Versatile', 'Fast'];
        break;
      case LargeLanguageModelType.ollama:
        features = ['Local', 'Open Source', 'Customizable'];
        break;
      case LargeLanguageModelType.mistralAI:
        features = ['Efficient', 'Multilingual', 'Code'];
        break;
      case LargeLanguageModelType.gemini:
        features = ['Multimodal', 'Google', 'Advanced'];
        break;
      case LargeLanguageModelType.none:
        features = ['None'];
        break;
    }

    return Wrap(
      spacing: 6,
      children: features
          .map(
            (feature) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                feature,
                style: TextStyle(
                  fontSize: 10,
                  color: colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          )
          .toList(),
    );
  }

  Future<void> switchModel(LargeLanguageModelType modelType) async {
    if (kDebugMode) {
      logger.i("switching model");
      BotToast.showText(text: "switching model");
    }
    setState(() {
      isLoading = true;
    });

    try {
      logger.i("Switching to ${modelType.displayName}...");
      ArtificialIntelligence.of(context).switchLargeLanguageModel(modelType);
      logger.i("${modelType.displayName} loaded successfully.");
      BotToast.showText(text: "${modelType.displayName} loaded successfully.");
    } catch (e) {
      logger.e("Error switching model: $e");
      BotToast.showText(text: "Error: ${e.toString()}");
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }
}
