import 'dart:io';
// import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:camera/camera.dart';
import 'package:convenient_test/convenient_test.dart';
import 'package:dart_ping_ios/dart_ping_ios.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:diogeneschatbot/constants/app_constants.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/controller.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/downloader.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/generator.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/loader.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/huggingface_selection.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/maid_page.dart';
import 'package:diogeneschatbot/models/maid_properties.dart';
import 'package:diogeneschatbot/routes/app_routes.dart';
import 'package:diogeneschatbot/services/auth_service.dart';
import 'package:diogeneschatbot/services/firebase_service.dart';
import 'package:diogeneschatbot/services/messaging_service.dart';
import 'package:diogeneschatbot/services/remote_config_service.dart';
import 'package:diogeneschatbot/services/service_locator.dart';
import 'package:diogeneschatbot/services/tts_service.dart';
import 'package:diogeneschatbot/util/device_info.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/widgets/auth_wrap_widget.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:logging/logging.dart';
import 'package:url_strategy/url_strategy.dart';

import 'features/offline_llm/data/local/classes/providers/desktop_navigator.dart';
import 'features/prompt/prompt_view_model.dart';
import 'features/recipes/recipes_view_model.dart';
import 'providers/chat_mindsearch_provider.dart';
import 'package:flutter_stripe/flutter_stripe.dart';

late CameraDescription camera;
late BaseDeviceInfo deviceInfo;

Future<void> main() async {
  Logger.root.level = Level.FINE;
  debugPaintSizeEnabled = false;
  WidgetsFlutterBinding.ensureInitialized();
  try {
    await _initializeApp();
  } catch (e, stackTrace) {
    // Log the error and stack trace
    print("Error _initializeApp: $e");
    print(stackTrace);

    // Show a toast message with the error using bot_toast
    BotToast.showText(
      text: "Error _initializeApp: $e",
      backgroundColor: Colors.red,
      contentColor: Colors.white,
      textStyle: TextStyle(color: Colors.white),
      duration: Duration(seconds: 5),
      animationDuration: Duration(seconds: 1),
    );
  }

  GoRouter.optionURLReflectsImperativeAPIs = true;

  // Start app
  if (!kIsWeb && (Platform.isIOS || Platform.isAndroid)) {
    DartPingIOS.register();
  }
  // TODO: Sentry temporarily disabled due to iOS build issues
  // await SentryFlutter.init(
  //   (options) {
  //     options.dsn = 'https://<EMAIL>/4508300144082944';
  //     print("Sentry initialized");
  //   },
  //   appRunner: ()  {
  //       print("Running app...");
  //     runApp(const DiogenesAIChatBotApp());
  //     },
  // );

  // Run app directly without Sentry
  print("Running app...");
  runApp(const DiogenesAIChatBotApp());
}

Future<void> _initializeApp() async {
  try {
    // Attempt to load environment variables from the appropriate .env file
    await dotenv.load(
      fileName: kReleaseMode ? ".env.production" : ".env.development",
    );
    print("Environment variables loaded successfully");
  } catch (e, stackTrace) {
    // Log the error and stack trace
    print("Error loading environment variables: $e");
    print(stackTrace);

    // Show a toast message with the error using bot_toast
    BotToast.showText(
      text: "Error loading environment variables: $e",
      backgroundColor: Colors.red,
      contentColor: Colors.white,
      textStyle: TextStyle(color: Colors.white),
      duration: Duration(seconds: 5),
      animationDuration: Duration(seconds: 1),
    );
  }

  await setupStripe();
  await FirebaseService.initialize();
  await RemoteConfigService.initialize();
  deviceInfo = await DeviceInfo.initialize(DeviceInfoPlugin());
  if (DeviceInfo.isPhysicalDeviceWithCamera(deviceInfo)) {
    final cameras = await availableCameras();
    camera = cameras.first;
  }
  setPathUrlStrategy();
  await setupGetIt();

  // Disable App Check in debug mode
  if (kDebugMode) {
    logger.i("In kDebugMode");
    // FirebaseAppCheck.instance
    //     .activate(webProvider: null); // For web, use webProvider: null
    await FirebaseAppCheck.instance.activate(
      webProvider: ReCaptchaV3Provider(
        '6LedE3sqAAAAAC2QbMUt16SsC6icKybnB9Eo31QB',
      ),
      // Set androidProvider to `AndroidProvider.debug`
      androidProvider: AndroidProvider.debug,
      appleProvider: AppleProvider.debug,
    );
  } else {
    // Enable App Check in production
    await FirebaseAppCheck.instance.activate(
      // You can also use a `ReCaptchaEnterpriseProvider` provider instance as an
      // argument for `webProvider`
      webProvider: ReCaptchaV3Provider(
        '6LedE3sqAAAAAC2QbMUt16SsC6icKybnB9Eo31QB',
      ),
      // Default provider for Android is the Play Integrity provider. You can use the "AndroidProvider" enum to choose
      // your preferred provider. Choose from:
      // 1. Debug provider
      // 2. Safety Net provider
      // 3. Play Integrity provider
      androidProvider: AndroidProvider.playIntegrity,
      // Default provider for iOS/macOS is the Device Check provider. You can use the "AppleProvider" enum to choose
      // your preferred provider. Choose from:
      // 1. Debug provider
      // 2. Device Check provider
      // 3. App Attest provider
      // 4. App Attest provider with fallback to Device Check provider (App Attest provider is only available on iOS 14.0+, macOS 14.0+)
      appleProvider: AppleProvider.appAttest,
    );
    logger.i("FirebaseAppCheck activated");
  }
}

Future<void> setupStripe() async {
  // Get the publishable key from the environment
  String stripePublishableKey =
      dotenv.env['STRIPE_PUBLISHABLE'] ??
      const String.fromEnvironment(
        "STRIPE_PUBLISHABLE",
        defaultValue: 'key not found',
      );

  // Apply Stripe configuration based on platform
  if (kIsWeb || (!kIsWeb && (Platform.isIOS || Platform.isAndroid))) {
    // For iOS and Android, web
    Stripe.publishableKey = stripePublishableKey;
    Stripe.merchantIdentifier = 'merchant.diogenes.ai.chatbot';
    Stripe.urlScheme = 'flutterstripe';
    // Apply the Stripe settings
    await Stripe.instance.applySettings();
  } else {
    // Handle unsupported platforms if needed
    logger.w("Unsupported platform for Stripe integration.");
  }
}

class DiogenesAIChatBotApp extends StatefulWidget {
  const DiogenesAIChatBotApp({super.key});

  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  @override
  State<DiogenesAIChatBotApp> createState() => _DiogenesAIChatBotAppState();
}

class _DiogenesAIChatBotAppState extends State<DiogenesAIChatBotApp> {
  bool _isInitialized = false;

  // bool _isPermissionRequested = false; // Track if permissions have been requested
  late final AuthService _authService;
  String googleAI_ApiKey =
      dotenv.env['GOOGLE_AI_API_KEY'] ??
      const String.fromEnvironment(
        "GOOGLE_AI_API_KEY",
        defaultValue: 'key not found',
      );

  MaidProperties? props;

  @override
  void initState() {
    super.initState();

    // init_gemini_models(); // Removed this call
    RemoteConfigService.initializeModels(
      googleAI_ApiKey: googleAI_ApiKey,
    ); // Added this call

    _authService = AuthService();
    _initializeMessaging();
    // Load MaidProperties after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      props = await MaidProperties.last;
      // After loading, call setState to rebuild the widget
      setState(() {});
    });
  }

  // Removed init_gemini_models() method

  @override
  void dispose() {
    _authService.dispose();
    super.dispose();
  }

  Future<void> _initializeMessaging() async {
    try {
      await MessagingService.initialize(DiogenesAIChatBotApp.navigatorKey);
      if (mounted) {
        setState(() => _isInitialized = true);
      }
    } catch (e) {
      logger.e("Messaging initialization failed: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    final recipesViewModel = SavedRecipesViewModel();
    if (!_isInitialized) return _buildLoadingScreen();
    // Show loading indicator if props is null
    if (props == null) {
      return const Center(
        child: CircularProgressIndicator(),
      ); // Loading indicator
    }

    final localeNotifier = getIt<ValueNotifier<Locale>>();
    return MultiProvider(
      providers: [
        Provider<AuthService>.value(value: _authService),
        ChangeNotifierProvider(
          create: (context) => TTSService(), // New TTSService provider
          key: ValueKey('ttsServiceProvider_DiogenesAIChatbot'), // Adding key
        ),
        ChangeNotifierProvider(
          create: (context) => ChatProvider(), // New ChatProvider
          key: ValueKey('chatProvider_DiogenesAIChatbot'), // Adding key
        ),
        ChangeNotifierProvider(
          create: (_) => PromptViewModel(
            multiModalModel:
                RemoteConfigService.geminiVisionPro!, // Use the static field
            textModel: RemoteConfigService.geminiPro!, // Use the static field
          ),
          key: ValueKey(
            'promptViewModelProvider_DiogenesAIChatbot',
          ), // Adding key
        ),
        ChangeNotifierProvider(
          create: (_) => recipesViewModel,
          key: ValueKey(
            'recipesViewModelProvider_DiogenesAIChatbot',
          ), // Adding key
        ),
        ChangeNotifierProvider(
          create: (context) => props!.appPreferences,
          key: ValueKey(
            'appPreferencesProvider_DiogenesAIChatbot',
          ), // Adding key
        ),
        ChangeNotifierProvider(
          create: (context) => props!.sessions,
          key: ValueKey('sessionsProvider_DiogenesAIChatbot'), // Adding key
        ),
        ChangeNotifierProvider(
          create: (context) => props!.characters,
          key: ValueKey('charactersProvider_DiogenesAIChatbot'), // Adding key
        ),
        ChangeNotifierProvider(
          create: (context) => props!.ai,
          key: ValueKey('aiProvider_DiogenesAIChatbot'), // Adding key
        ),
        ChangeNotifierProvider(
          create: (context) => props!.user,
          key: ValueKey('userProvider_DiogenesAIChatbot'), // Adding key
        ),
        ChangeNotifierProvider(
          create: (context) => HuggingfaceSelection(),
          key: ValueKey(
            'huggingfaceSelectionProvider_DiogenesAIChatbot',
          ), // Adding key
        ),
        ChangeNotifierProvider(create: (context) => DesktopNavigator()),
        ChangeNotifierProvider(create: (context) => IndexPageModelDownloader()),
        ChangeNotifierProvider(create: (context) => IndexPageModelLoader()),
        ChangeNotifierProvider(create: (context) => IndexPageGenerator()),
        ChangeNotifierProvider(create: (context) => IndexPageController()),
      ],
      child: StreamBuilder<AuthStatus>(
        stream: _authService.authStatusStream,
        builder: (context, snapshot) {
          return ValueListenableBuilder<Locale>(
            valueListenable: localeNotifier,
            builder: (context, locale, child) {
              return ConvenientTestWrapperWidget(
                child: GetMaterialApp(
                  navigatorKey: DiogenesAIChatBotApp.navigatorKey,
                  title: AppConstants.appTitle,
                  theme: AppTheme.lightTheme,
                  darkTheme: AppTheme.darkTheme,
                  themeMode: ThemeMode
                      .system, // Automatically switch based on system preference
                  locale: locale,
                  localizationsDelegates: const [
                    AppLocalizations.delegate,
                    GlobalMaterialLocalizations.delegate,
                    GlobalWidgetsLocalizations.delegate,
                    GlobalCupertinoLocalizations.delegate,
                  ],
                  supportedLocales: AppLocalizations.supportedLocales,
                  home: AuthWrapper(
                    authStatus: snapshot.data ?? AuthStatus.unauthenticated,
                    authService: _authService,
                  ),
                  onGenerateRoute: AppRoutes.onGenerateRoute,
                  debugShowCheckedModeBanner: false,
                  builder: BotToastInit(),
                  navigatorObservers: [
                    BotToastNavigatorObserver(),
                    FirebaseAnalyticsObserver(
                      analytics: FirebaseAnalytics.instance,
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildLoadingScreen() {
    return MaterialApp(
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              const Text(
                'Loading... Please wait.',
                style: TextStyle(fontSize: 18),
              ),
              const SizedBox(height: 16),
              const Text(
                'To enhance your experience, please enable notification permissions. You can close the app, enable notifications in system settings. Then open it again.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
