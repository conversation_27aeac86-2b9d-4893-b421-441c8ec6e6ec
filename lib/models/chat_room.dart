import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:diogeneschatbot/models/profile.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

class ChatRoom {
  final String chatRoomId;
  final List<String> memberIds;
  final String? groupName; // Set this to null for personal chats
  final String? groupImageUrl; // Set this to null for personal chats
  final Set<String>? tokens; // fcm tokens
  final String?
  groupAdminId; // admin id for group chat. it is null for chat room.

  ChatRoom({
    required this.chatRoomId,
    required this.memberIds,
    this.groupName,
    this.groupImageUrl,
    this.tokens,
    this.groupAdminId,
  });

  Map<String, dynamic> toMap() {
    return {
      'chatRoomId': chatRoomId,
      'memberIds': memberIds,
      'groupName': groupName,
      'groupImageUrl': groupImageUrl,
      'tokens': tokens?.toSet(),
      "groupAdminId": groupAdminId,
    };
  }

  factory ChatRoom.fromMap(Map<String, dynamic> map) {
    return ChatRoom(
      chatRoomId: map['chatRoomId'],
      memberIds: List<String>.from(map['memberIds']),
      groupName: map['groupName'],
      groupImageUrl: map['groupImageUrl'],
      tokens: map['tokens'] != null ? Set<String>.from(map['tokens']) : null,
      groupAdminId: map['groupAdminId'],
    );
  }

  factory ChatRoom.fromFirestore(DocumentSnapshot doc) {
    return ChatRoom.fromMap(doc.data() as Map<String, dynamic>);
  }
}

class Message {
  final String messageId;
  final String chatRoomId;
  final String senderId;
  final String content;
  final DateTime timestamp;
  final ChatMessageType messageType;
  final bool isFromAi;
  final bool isRead;

  Message({
    required this.messageId,
    required this.chatRoomId,
    required this.senderId,
    required this.content,
    required this.timestamp,
    required this.messageType,
    this.isFromAi = false,
    this.isRead = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'messageId': messageId,
      'chatRoomId': chatRoomId,
      'senderId': senderId,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'messageType': messageType.index,
      "isFromAi": isFromAi,
      "isRead": isRead,
    };
  }

  factory Message.fromMap(Map<String, dynamic> map) {
    bool isFromAi = map['isFromAi'] ?? false;
    bool isRead = map['isRead'] ?? false;
    return Message(
      messageId: map['messageId'],
      chatRoomId: map['chatRoomId'],
      senderId: map['senderId'],
      content: map['content'],
      timestamp: DateTime.parse(map['timestamp']),
      messageType: ChatMessageType.values[map['messageType']],
      isFromAi: isFromAi,
      isRead: isRead,
    );
  }

  factory Message.fromFirestore(DocumentSnapshot doc) {
    return Message.fromMap(doc.data() as Map<String, dynamic>);
  }
}

enum ChatMessageType { text, image, video, document, audio }

class ChatRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final ProfileRepository _profileRepository = ProfileRepository();

  Future<void> subscribeToChatRoom(String chatRoomId) async {
    String? fcmToken = await _firebaseMessaging.getToken();

    await _firestore
        .collection('chatRooms')
        .doc(chatRoomId)
        .collection('tokens')
        .doc(fcmToken)
        .set({'token': fcmToken});
  }

  Future<void> unsubscribeFromChatRoom(String chatRoomId) async {
    String? fcmToken = await _firebaseMessaging.getToken();

    await _firestore
        .collection('chatRooms')
        .doc(chatRoomId)
        .collection('tokens')
        .doc(fcmToken)
        .delete();
  }

  Future<ChatRoom?> getChatRoom(String chatRoomId) async {
    DocumentSnapshot doc = await _firestore
        .collection('chatRooms')
        .doc(chatRoomId)
        .get();

    if (doc.exists) {
      ChatRoom chatRoom = ChatRoom.fromFirestore(doc);
      return chatRoom;
    } else {
      throw Exception('Chat room not found');
    }
  }

  Future<List<String>> getMemberIdsFromChatRoomId(String chatRoomId) async {
    DocumentSnapshot doc = await _firestore
        .collection('chatRooms')
        .doc(chatRoomId)
        .get();

    if (doc.exists) {
      ChatRoom chatRoom = ChatRoom.fromFirestore(doc);
      return chatRoom.memberIds;
    } else {
      throw Exception('Chat room not found');
    }
  }

  static String generateChatRoomId(List<String> memberIds) {
    memberIds.sort();
    return memberIds.join("_");
  }

  // Create chat room
  Future<void> createChatRoom(ChatRoom chatRoom) async {
    DateTime startTime = DateTime.now().toUtc();
    DocumentReference docRef = _firestore
        .collection('chatRooms')
        .doc(chatRoom.chatRoomId);

    // Subscribe every member to the chat room upon creation
    DateTime beforeGetDeviceTokens = DateTime.now().toUtc();
    Set<String> deviceTokens = await _profileRepository.getDeviceTokens(
      chatRoom.memberIds.toList(),
    );
    DateTime afterGetDeviceTokens = DateTime.now().toUtc();
    logger.d(
      'Time to get device tokens: ${afterGetDeviceTokens.difference(beforeGetDeviceTokens).inMilliseconds} ms',
    );

    WriteBatch batch = _firestore.batch();

    for (String fcmToken in deviceTokens) {
      final tokenDoc = _firestore
          .collection('chatRooms')
          .doc(chatRoom.chatRoomId)
          .collection('tokens')
          .doc(fcmToken);
      batch.set(tokenDoc, {'token': fcmToken});
    }

    DateTime beforeBatchCommit = DateTime.now().toUtc();
    await batch.commit();
    DateTime afterBatchCommit = DateTime.now().toUtc();
    logger.d(
      'Time to commit batch: ${afterBatchCommit.difference(beforeBatchCommit).inMilliseconds} ms',
    );

    DateTime beforeSetChatRoom = DateTime.now().toUtc();
    await docRef.set(chatRoom.toMap(), SetOptions(merge: true));
    DateTime afterSetChatRoom = DateTime.now().toUtc();
    logger.d(
      'Time to set chat room: ${afterSetChatRoom.difference(beforeSetChatRoom).inMilliseconds} ms',
    );

    DateTime endTime = DateTime.now().toUtc();
    logger.d('Total time: ${endTime.difference(startTime).inMilliseconds} ms');
  }

  // Save chat history
  Future<void> saveMessage(Message message) {
    return _firestore
        .collection('messages')
        .doc(message.messageId)
        .set(message.toMap());
  }

  // Retrieve chat history with optional parameters
  Stream<QuerySnapshot> getChatHistory(
    String chatRoomId, {
    int limit = 200,
    int daysRange = 7,
  }) {
    final DateTime cutoffDate = DateTime.now().toUtc().subtract(
      Duration(days: daysRange),
    );

    return _firestore
        .collection('messages')
        .where('chatRoomId', isEqualTo: chatRoomId)
        .where(
          'timestamp',
          isGreaterThanOrEqualTo: cutoffDate.toIso8601String(),
        )
        .orderBy('timestamp', descending: false)
        .limit(limit)
        .snapshots();
  }

  // Update read receipt status
  Future<void> updateReadReceipt(String messageId, bool isRead) {
    return _firestore.collection('messages').doc(messageId).update({
      'isRead': isRead,
    });
  }

  // Add/remove members
  Future<void> addMembers(String chatRoomId, List<String> memberIds) async {
    final batch = _firestore.batch();

    // Add members to the chat room
    batch.update(_firestore.collection('chatRooms').doc(chatRoomId), {
      'memberIds': FieldValue.arrayUnion(memberIds),
    });

    for (String memberId in memberIds) {
      // Get the profile of the member to retrieve their device tokens
      Profile? memberProfile = await _profileRepository.getProfile(memberId);
      if (memberProfile != null && memberProfile.deviceTokens != null) {
        for (String token in memberProfile.deviceTokens!) {
          batch.set(
            _firestore
                .collection('chatRooms')
                .doc(chatRoomId)
                .collection('tokens')
                .doc(token),
            {'token': token},
          );
        }
      }
    }

    await batch.commit();
  }

  Future<void> removeMembers(String chatRoomId, List<String> memberIds) async {
    final batch = _firestore.batch();

    // Remove members from the chat room
    batch.update(_firestore.collection('chatRooms').doc(chatRoomId), {
      'memberIds': FieldValue.arrayRemove(memberIds),
    });

    for (String memberId in memberIds) {
      // Get the profile of the member to retrieve their device tokens
      Profile? memberProfile = await _profileRepository.getProfile(memberId);
      if (memberProfile != null && memberProfile.deviceTokens != null) {
        for (String token in memberProfile.deviceTokens!) {
          batch.delete(
            _firestore
                .collection('chatRooms')
                .doc(chatRoomId)
                .collection('tokens')
                .doc(token),
          );
        }
      }
    }

    await batch.commit();
  }

  Stream<List<ChatRoom>> getUserChatRooms(String userId) {
    return _firestore
        .collection('chatRooms')
        .where('memberIds', arrayContains: userId)
        .snapshots()
        .handleError((error) {
          print('❌ Error in getUserChatRooms stream: $error');
        })
        .map((snapshot) {
          print('📊 Firestore snapshot received:');
          print('  - Docs count: ${snapshot.docs.length}');

          final chatRooms = snapshot.docs.map((doc) {
            return ChatRoom.fromMap(doc.data());
          }).toList();

          print('✅ Returning ${chatRooms.length} chat rooms');
          return chatRooms;
        });
  }

  Future<void> deleteGroupChat(String chatRoomId) {
    return _firestore.collection('chatRooms').doc(chatRoomId).delete();
  }
}
