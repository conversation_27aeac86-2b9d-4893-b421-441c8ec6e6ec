import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:diogeneschatbot/models/bot.dart';
import 'package:diogeneschatbot/models/conversation.dart';
import 'package:diogeneschatbot/models/profile.dart';
import 'package:diogeneschatbot/pages/chat_bot_page.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/avatar_widget.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import 'package:random_avatar/random_avatar.dart';

class BotConversationListPage extends StatefulWidget {
  //TODO: add conversations that current user with any bot(other people's bot)
  final String botId;

  BotConversationListPage({required this.botId});

  @override
  _BotConversationListPageState createState() =>
      _BotConversationListPageState();
}

class _BotConversationListPageState extends State<BotConversationListPage>
    with TickerProviderStateMixin {
  final ProfileRepository profileRepository = ProfileRepository();
  final BotRepository botRepository = BotRepository();
  final ConversationRepository _conversationRepository =
      ConversationRepository();

  bool _isDeleting = false;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBarStyles.primary(
        title: 'Bot Conversations',
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(100),
          child: Column(
            children: [
              // Search bar
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search conversations...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ),
              // Tab bar
              TabBar(
                controller: _tabController,
                labelColor: Colors.white,
                unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
                indicatorColor: Colors.white,
                tabs: const [
                  Tab(icon: Icon(Icons.person), text: 'My Conversations'),
                  Tab(icon: Icon(Icons.people), text: 'Other Conversations'),
                ],
              ),
            ],
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: isDark ? AppTheme.darkGradient : AppTheme.lightGradient,
        ),
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildConversationsList(true),
            _buildConversationsList(false),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToBotChatPage(
          FirebaseAuth.instance.currentUser!.uid,
          widget.botId,
          null,
        ),
        icon: const Icon(Icons.add_comment),
        label: const Text('New Chat'),
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildConversationsList(bool myConversations) {
    return StreamBuilder<QuerySnapshot<Map<String, dynamic>>>(
      stream: _conversationRepository
          .getConversationQueryByBotId(widget.botId)
          .orderBy('createdAt', descending: true)
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: EnhancedLoading(
              type: LoadingType.pulse,
              message: 'Loading conversations...',
              showMessage: true,
            ),
          );
        }

        if (snapshot.hasError) {
          return _buildErrorState(snapshot.error.toString());
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return _buildEmptyState(myConversations);
        }

        final conversations = snapshot.data!.docs;

        // Filter conversations based on user and search query
        final filteredConversations = conversations.where((conversation) {
          final userId = conversation['userId'];
          final isUserMatch =
              (myConversations &&
                  userId == FirebaseAuth.instance.currentUser!.uid) ||
              (!myConversations &&
                  userId != FirebaseAuth.instance.currentUser!.uid);

          if (!isUserMatch) return false;

          // Apply search filter if query is not empty
          if (_searchQuery.isNotEmpty) {
            // TODO: Add search functionality based on conversation content
            // For now, we'll search by creation date or user info
            return true; // Placeholder - implement actual search logic
          }

          return true;
        }).toList();

        if (filteredConversations.isEmpty) {
          return _buildEmptyState(
            myConversations,
            isFiltered: _searchQuery.isNotEmpty,
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            // Trigger a rebuild by calling setState
            setState(() {});
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredConversations.length,
            itemBuilder: (context, index) {
              final conversation = filteredConversations[index];
              final userId = conversation['userId'];
              final botId = conversation['botId'];

              return FutureBuilder(
                future: Future.wait([
                  profileRepository.getProfile(userId),
                  botRepository.getBot(botId),
                  _conversationRepository.getMessagesCount(conversation.id),
                ]),
                builder: (context, AsyncSnapshot<List> snapshot) {
                  if (snapshot.hasData) {
                    final Profile? user = snapshot.data![0];
                    final Bot bot = snapshot.data![1];
                    final messageCount = snapshot.data![2];
                    final userName = user != null ? user.name : 'Anonymous';
                    final botName = bot.name;
                    final createdAt = DateTime.parse(conversation['createdAt']);
                    final formattedCreatedAt = DateFormat(
                      'yyyy/MM/dd HH:mm:ss',
                    ).format(createdAt);

                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: EnhancedCard(
                        onTap: () => _navigateToBotChatPage(
                          userId,
                          botId,
                          conversation.id,
                        ),
                        child: _buildConversationItem(
                          user,
                          bot,
                          userName,
                          botName,
                          formattedCreatedAt,
                          messageCount,
                          conversation.id,
                        ),
                      ),
                    );
                  } else {
                    return const Center(
                      child: EnhancedLoading(
                        type: LoadingType.dots,
                        message: 'Loading...',
                        showMessage: true,
                      ),
                    );
                  }
                },
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildConversationItem(
    Profile? user,
    Bot bot,
    String userName,
    String botName,
    String formattedCreatedAt,
    int messageCount,
    String conversationId,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with user and bot info
        Row(
          children: [
            // User avatar
            user != null
                ? AvatarWidget(profile: user)
                : CircleAvatar(
                    radius: 20,
                    backgroundColor: AppTheme.primaryGreen,
                    child: const Icon(Icons.person, color: Colors.white),
                  ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    userName,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      RandomAvatar(
                        bot.id,
                        trBackground: true,
                        height: 16,
                        width: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Bot: $botName',
                        style: TextStyle(
                          color: AppTheme.primaryGreen,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Delete button
            IconButton(
              icon: Icon(
                Icons.delete_outline,
                color: Theme.of(context).colorScheme.error,
              ),
              onPressed: () => _showDeleteDialog(conversationId),
              tooltip: 'Delete conversation',
            ),
          ],
        ),
        const SizedBox(height: 12),
        // Stats row
        Row(
          children: [
            Icon(
              Icons.access_time,
              size: 16,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            const SizedBox(width: 4),
            Text(
              formattedCreatedAt,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppTheme.accentBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.chat_bubble_outline,
                    size: 14,
                    color: AppTheme.accentBlue,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '$messageCount',
                    style: TextStyle(
                      color: AppTheme.accentBlue,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: EnhancedCard(
        margin: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Something went wrong',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => setState(() {}),
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(bool myConversations, {bool isFiltered = false}) {
    return Center(
      child: EnhancedCard(
        margin: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isFiltered ? Icons.search_off : Icons.chat_bubble_outline,
              size: 64,
              color: AppTheme.primaryGreen,
            ),
            const SizedBox(height: 16),
            Text(
              isFiltered
                  ? 'No conversations found'
                  : myConversations
                  ? 'No conversations yet'
                  : 'No other conversations',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              isFiltered
                  ? 'Try adjusting your search terms'
                  : myConversations
                  ? 'Start a new conversation with this bot'
                  : 'No other users have conversations with this bot yet',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            if (isFiltered) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () => _searchController.clear(),
                icon: const Icon(Icons.clear),
                label: const Text('Clear Search'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showDeleteDialog(String conversationId) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('Confirm Delete'),
              content: _isDeleting
                  ? const Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Deleting conversation...'),
                      ],
                    )
                  : const Text(
                      'Are you sure you want to delete this conversation? This action cannot be undone.',
                    ),
              actions: _isDeleting
                  ? []
                  : [
                      TextButton(
                        onPressed: () => Navigator.of(dialogContext).pop(),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () async {
                          setDialogState(() {
                            _isDeleting = true;
                          });

                          try {
                            final deleted = await _conversationRepository
                                .deleteConversation(conversationId);

                            if (mounted) {
                              Navigator.of(dialogContext).pop();

                              if (context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      deleted
                                          ? 'Conversation deleted successfully'
                                          : 'Failed to delete conversation',
                                    ),
                                    backgroundColor: deleted
                                        ? AppTheme.primaryGreen
                                        : Theme.of(context).colorScheme.error,
                                    behavior: SnackBarBehavior.floating,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                );
                              }
                            }
                          } finally {
                            if (mounted) {
                              setState(() {
                                _isDeleting = false;
                              });
                            }
                          }
                        },
                        style: TextButton.styleFrom(
                          foregroundColor: Theme.of(context).colorScheme.error,
                        ),
                        child: const Text('Delete'),
                      ),
                    ],
            );
          },
        );
      },
    );
  }

  Future<void> _navigateToBotChatPage(
    String? userId,
    String botId,
    String? conversationId,
  ) async {
    if (!mounted) return;

    try {
      final selectedBot = await botRepository.getBot(widget.botId);
      if (mounted && context.mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BotChatPage(
              userId: userId!,
              selectedBot: selectedBot,
              conversationId: conversationId,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load bot: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }
}
