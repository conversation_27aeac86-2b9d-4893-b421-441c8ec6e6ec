import 'dart:math';

import 'package:diogeneschatbot/models/bot.dart';
import 'package:diogeneschatbot/pages/bot_conversation_list_page.dart';
import 'package:diogeneschatbot/pages/bot_form_page.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_button.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:random_avatar/random_avatar.dart';

/// Enhanced bot management page for creating, editing, and managing AI bots
/// Provides a comprehensive interface for bot operations with modern UI
class BotPage extends StatefulWidget {
  const BotPage({super.key});

  @override
  State<BotPage> createState() => _BotPageState();
}

class _BotPageState extends State<BotPage> {
  // Configuration
  final String? diogenesWebUrl =
      dotenv.env['DIOGENES_WEB_URL'] ??
      const String.fromEnvironment("DIOGENES_WEB_URL");

  // Dependencies
  final BotRepository _botRepository = BotRepository();

  // State management
  List<Bot> _bots = [];
  bool _isLoading = false;
  bool _isDeleting = false;
  String _searchQuery = '';

  // Controllers
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchBots();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
      appBar: AppBarStyles.primary(
        title: 'AI Bots & Agents',
        actions: [
          // Search button
          IconButton(
            icon: Icon(Icons.search, color: Colors.white),
            tooltip: 'Search bots',
            onPressed: () {
              // TODO: Implement search functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Search functionality coming soon!'),
                ),
              );
            },
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: LoadingStyles.primary(message: 'Loading your bots...'),
            )
          : _bots.isEmpty
          ? _buildEmptyState(context, theme)
          : _buildBotList(context, theme, isDark),
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppTheme.primaryGreen,
        foregroundColor: Colors.white,
        tooltip: 'Create New Bot',
        onPressed: _createBot,
        child: const Icon(Icons.add),
      ),
    );
  }

  // Future<String> createBotChatDynamicLink(String botId) async {
  //   if (kIsWeb) {
  //     return '$diogenesWebUrl/chat?botId=$botId';
  //   }
  //   final DynamicLinkParameters dynamicLinkParams = DynamicLinkParameters(
  //     uriPrefix: 'https://diogenes.page.link',
  //     link: Uri.parse('$diogenesWebUrl/chat?botId=$botId'),
  //     androidParameters: AndroidParameters(
  //       packageName: 'com.diogenes.ai.chatbot',
  //     ),
  //     iosParameters: IOSParameters(
  //       bundleId: 'com.diogenes.ai.chatbot',
  //     ),
  //   );

  //   final ShortDynamicLink shortDynamicLink =
  //       await FirebaseDynamicLinks.instance.buildShortLink(dynamicLinkParams);
  //   return shortDynamicLink.shortUrl.toString();
  // }

  void _fetchBots() async {
    setState(() {
      _isLoading = true;
    });
    final user = FirebaseAuth.instance.currentUser;
    final ownerId = user != null ? user.uid : '';
    final bots = await _botRepository.getBotsByOwnerId(ownerId);
    setState(() {
      _bots = bots;
      _isLoading = false;
    });
  }

  void _createBot() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => BotFormPage()),
    ).then((createdBot) {
      if (createdBot != null) {
        _fetchBots();
      }
    });
  }

  void _editBot(Bot bot) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => BotFormPage(bot: bot)),
    ).then((editedBot) {
      if (editedBot != null) {
        _fetchBots();
      }
    });
  }

  /// Builds an enhanced empty state widget with helpful messaging
  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return Center(
      child: CardStyles.outlined(
        margin: const EdgeInsets.all(32),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.smart_toy,
              size: 64,
              color: AppTheme.primaryGreen.withValues(alpha: 0.6),
            ),
            const SizedBox(height: 16),
            Text(
              'No bots created yet',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: AppTheme.primaryGreen,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first AI bot to get started with automated conversations and assistance.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.hintColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ButtonStyles.primary(
              text: 'Create Bot',
              icon: Icons.add,
              onPressed: _createBot,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the enhanced bot list with modern card styling
  Widget _buildBotList(BuildContext context, ThemeData theme, bool isDark) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _bots.length,
      itemBuilder: (context, index) {
        final bot = _bots[index];
        return _buildBotCard(context, theme, isDark, bot, index);
      },
    );
  }

  /// Builds an individual bot card with enhanced styling
  Widget _buildBotCard(
    BuildContext context,
    ThemeData theme,
    bool isDark,
    Bot bot,
    int index,
  ) {
    // Generate consistent colors based on bot ID
    final colorSeed = bot.id.hashCode;
    final hue = (colorSeed % 360).toDouble();
    final botColor = HSVColor.fromAHSV(1.0, hue, 0.6, 0.8).toColor();

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: CardStyles.elevated(
        child: InkWell(
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => BotConversationListPage(botId: bot.id),
              ),
            );
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Bot Avatar
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: botColor, width: 2),
                    boxShadow: [
                      BoxShadow(
                        color: botColor.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: RandomAvatar(
                    bot.id,
                    trBackground: true,
                    height: 48,
                    width: 48,
                  ),
                ),
                const SizedBox(width: 16),

                // Bot Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        bot.name,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primaryGreen,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        bot.description.isNotEmpty
                            ? bot.description
                            : 'No description provided',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.hintColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),

                // Actions Menu
                _buildBotActionsMenu(context, bot),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Builds the enhanced actions menu for each bot
  Widget _buildBotActionsMenu(BuildContext context, Bot bot) {
    return PopupMenuButton<String>(
      icon: Icon(Icons.more_vert, color: AppTheme.primaryGreen),
      tooltip: 'Bot actions',
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      itemBuilder: (BuildContext context) => [
        PopupMenuItem<String>(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, color: AppTheme.primaryGreen, size: 20),
              const SizedBox(width: 12),
              const Text('Edit Bot'),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, color: Colors.red, size: 20),
              const SizedBox(width: 12),
              const Text('Delete Bot'),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'share',
          child: Row(
            children: [
              Icon(Icons.share, color: AppTheme.secondaryGreen, size: 20),
              const SizedBox(width: 12),
              const Text('Share Bot'),
            ],
          ),
        ),
      ],
      onSelected: (String value) {
        switch (value) {
          case 'edit':
            _editBot(bot);
            break;
          case 'delete':
            _showDeleteConfirmation(context, bot);
            break;
          case 'share':
            _shareBot(bot);
            break;
        }
      },
    );
  }

  /// Shows an enhanced delete confirmation dialog
  void _showDeleteConfirmation(BuildContext context, Bot bot) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.red, size: 24),
              const SizedBox(width: 8),
              const Text('Delete Bot'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Are you sure you want to delete "${bot.name}"?'),
              const SizedBox(height: 8),
              Text(
                'This action cannot be undone. All conversations and data associated with this bot will be permanently deleted.',
                style: TextStyle(
                  color: Theme.of(context).hintColor,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: TextStyle(color: Theme.of(context).hintColor),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _deleteBot(bot);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  /// Deletes a bot with loading state and error handling
  Future<void> _deleteBot(Bot bot) async {
    try {
      setState(() {
        _isDeleting = true;
      });

      await _botRepository.deleteBot(bot.id);
      _fetchBots();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green),
                const SizedBox(width: 16),
                Text('Bot "${bot.name}" deleted successfully'),
              ],
            ),
            backgroundColor: Colors.green.withValues(alpha: 0.1),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.red),
                const SizedBox(width: 16),
                Text('Failed to delete bot: ${e.toString()}'),
              ],
            ),
            backgroundColor: Colors.red.withValues(alpha: 0.1),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDeleting = false;
        });
      }
    }
  }

  /// Handles bot sharing functionality
  void _shareBot(Bot bot) {
    // TODO: Implement bot sharing functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Bot sharing functionality coming soon!')),
    );
  }
}
