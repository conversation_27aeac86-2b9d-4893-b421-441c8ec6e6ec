import 'dart:async';
import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:diogeneschatbot/controllers/chat_ai_controller.dart';
import 'package:diogeneschatbot/features/code_editor/presentation/pages/code_editor_page.dart';
import 'package:diogeneschatbot/features/writing/presentation/pages/writing_sessions_page.dart';
import 'package:diogeneschatbot/models/usage.dart';
import 'package:diogeneschatbot/pages/chathistory_page.dart';
import 'package:diogeneschatbot/repository/firebase_storage_repository.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/util/util_token.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/widgets/audio_chat_widget.dart';
import 'package:diogeneschatbot/widgets/audio_recorder_widget.dart';
import 'package:diogeneschatbot/widgets/chat_message_item_widget.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:diogeneschatbot/widgets/file_selection_action_grid.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class ChatPage extends StatefulWidget {
  final String title;
  final Usage type;

  ChatPage({super.key, required this.title, required this.type});

  @override
  _ChatPageState createState() => _ChatPageState(type);
}

class _ChatPageState extends State<ChatPage> {
  StreamController<String> _answerStreamController =
      StreamController<String>.broadcast();

  List<String> imageUrls = [];
  List<String> downloadImageUrls = [];
  List<String> audioUrls = [];
  List<String> textUrls = [];
  List<String> videoUrls = [];

  String? currentUserId;
  final imagePicker = ImagePicker();
  bool _isLoading = false;

  FirebaseStorageRepository _firebaseStorageRepository =
      FirebaseStorageRepository();

  void _setInitialState() {
    String? userId = FirebaseAuth.instance.currentUser?.uid;
    setState(() {
      currentUserId = userId;
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _answerStreamController.close();
    super.dispose();
  }

  void setLoading(bool value) {
    setState(() {
      _isLoading = value;
    });
  }

  late ChatAIController _chatController;

  @override
  void initState() {
    super.initState();
    _setInitialState();
    _chatController = ChatAIController(
      _chatMessages,
      currentUserId,
      setLoading,
      _answerStreamController,
      usage,
      setState,
    );
  }

  final TextEditingController _textController = TextEditingController();
  List<Map<String, dynamic>> _chatMessages = [];
  final ScrollController _scrollController = ScrollController();

  int _wordCount = 0;
  int _letterCount = 0;
  late Usage usage;

  _ChatPageState(Usage type) {
    usage = type;
  }

  void _handleTranscription(String transcript) {
    logger.d('Transcribed text: $transcript');
    setState(() {
      _textController.text = transcript;
      _updateWordAndLetterCount(transcript);
    });
  }

  void _updateWordAndLetterCount(String value) {
    setState(() {
      _wordCount = Util_Token.countTokens(value.trim());
      _letterCount = value.trim().length;
    });
  }

  void setChatMessages(String responseMessage) {
    setState(() {
      _chatMessages.last['answer'] = responseMessage;
    });
  }

  void _handleFileUrls(List<String> fileUrls) {
    // Display toast before processing
    BotToast.showText(
      text: "Processing file URLs...",
      duration: Duration(seconds: 2),
      align: Alignment.bottomCenter,
    );

    // Log before handling
    logger.d("Started processing file URLs: $fileUrls");

    // Initialize lists to categorize the files, reset to 0
    downloadImageUrls = [];
    // Download the files and categorize them
    imageUrls = [];
    audioUrls = [];
    textUrls = [];
    videoUrls = [];

    for (String url in fileUrls) {
      // Log the URL being processed
      logger.d("Processing URL: $url");

      // Parse the URL and remove any query parameters manually
      Uri uri = Uri.parse(url);
      String cleanUrl =
          uri.scheme + "://" + uri.host + uri.path; // Remove the query part

      // Log the cleaned URL
      logger.d("Clean URL (query removed): $cleanUrl");

      // Extract the file extension by isolating the part after the last slash
      String fileExtension = cleanUrl
          .split('/')
          .last
          .split('.')
          .last
          .toLowerCase();

      // Check for the file extension and categorize
      switch (fileExtension) {
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
          downloadImageUrls.add(url);
          imageUrls.add(cleanUrl); // Add to image list
          break;
        case 'mp3':
        case 'wav':
        case 'ogg':
        case 'aac':
          audioUrls.add(cleanUrl); // Add to audio list
          break;
        case 'txt':
        case 'md':
        case 'html':
        case 'json':
          textUrls.add(cleanUrl); // Add to text list
          break;
        case 'mp4':
        case 'mov':
        case 'avi':
        case 'mkv':
          videoUrls.add(cleanUrl); // Add to video list
          break;
        default:
          logger.e('Unsupported file type: $url');
          break;
      }
    }

    setState(() {
      // images updated
    });

    // Log after processing
    logger.d(
      "Finished processing file URLs. Categorized URLs: \n"
      "Image URLs: $imageUrls\n"
      "Audio URLs: $audioUrls\n"
      "Text URLs: $textUrls\n"
      "Video URLs: $videoUrls",
    );

    // Display toast after processing
    BotToast.showText(
      text: "File URLs processed successfully.",
      duration: Duration(seconds: 2),
      align: Alignment.bottomCenter,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
      appBar: AppBarStyles.primary(
        title: widget.title,
        actions: [
          // Code Editor Action for Programming
          if (usage.type == UsageType.program)
            Container(
              margin: EdgeInsets.only(right: 8),
              child: IconButton(
                icon: Icon(Icons.code, color: Colors.white),
                tooltip: 'Code Editor',
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          CodeEditorPage(initialCode: 'Hello world'),
                    ),
                  );
                },
              ),
            ),

          // Writing Sessions Action
          if (usage.type == UsageType.write ||
              usage.type == UsageType.gptResearcher)
            Container(
              margin: EdgeInsets.only(right: 8),
              child: IconButton(
                key: Key("WritingSessionsButtonOnChatPage"),
                icon: Icon(Icons.article, color: Colors.white),
                tooltip: 'Writing Sessions',
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => WritingSessionsPage(usage: usage),
                    ),
                  );
                },
              ),
            )
          else
            Container(
              margin: EdgeInsets.only(right: 8),
              child: IconButton(
                key: Key("ChatHistoryButtonOnChatPage"),
                icon: Icon(Icons.history, color: Colors.white),
                tooltip: 'Chat History',
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ChatHistoryPage(usage: usage),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          if (widget.type.type == UsageType.program &&
              constraints.maxWidth > 1200) {
            return Row(
              children: [
                Expanded(child: _buildChatBody()), // ChatPage content
                VerticalDivider(),
                Expanded(
                  child: CodeEditorPage(
                    initialCode: 'Hello world',
                  ), // CodeEditorPage content
                ),
              ],
            );
          } else {
            return _buildChatBody();
          }
        },
      ),
    );
  }

  Widget _buildChatBody() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      children: <Widget>[
        Expanded(
          child: Builder(
            builder: (BuildContext context) {
              return ListView.builder(
                controller: _scrollController,
                itemCount: _chatMessages.length,
                itemBuilder: (BuildContext context, int index) {
                  final Map<String, dynamic> chatMessage = _chatMessages[index];
                  return ChatMessageItem(
                    chatMessage: chatMessage,
                    answerStreamController: _answerStreamController,
                    isLastMessage: index == _chatMessages.length - 1,
                  );
                },
              );
            },
          ),
        ),
        Divider(height: 1.0),
        // Display selected image thumbnails here
        // Display selected image thumbnails only if images are selected
        if (imageUrls.isNotEmpty) _buildEnhancedImageThumbnails(),

        // Enhanced Input Area
        _buildEnhancedInputArea(context, theme, isDark),
      ],
    );
  }

  Future<void> onSendPressed(BuildContext context) async {
    final message = _textController.text.trim();

    if (message.isNotEmpty) {
      _chatController.fetchRealTimeUpdates(
        context,
        message,
        usage,
        downloadImageUrls,
        imageUrls, // Accept imageUrls list
        audioUrls, // Accept audioUrls list
        textUrls, // Accept textUrls list
        videoUrls, // Accept videoUrls list
      );

      await Future.delayed(Duration.zero);
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );

      _textController.clear();
      setState(() {
        downloadImageUrls = [];
        _isLoading = true;
        _wordCount = 0;
        _letterCount = 0;
      });
    }
  }

  /// Enhanced image thumbnails with better styling
  Widget _buildEnhancedImageThumbnails() {
    return CardStyles.outlined(
      margin: EdgeInsets.all(16.0),
      padding: EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.image, color: AppTheme.primaryGreen, size: 20),
              SizedBox(width: 8),
              Text(
                'Selected Images (${downloadImageUrls.length})',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryGreen,
                ),
              ),
              Spacer(),
              IconButton(
                icon: Icon(Icons.clear, size: 18),
                onPressed: () {
                  setState(() {
                    downloadImageUrls.clear();
                    imageUrls.clear();
                  });
                },
                tooltip: 'Clear all images',
              ),
            ],
          ),
          SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: downloadImageUrls.map((imageUrl) {
                return Container(
                  margin: EdgeInsets.only(right: 8.0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        width: 60,
                        height: 60,
                        color: AppTheme.lightSurface,
                        child: LoadingStyles.primary(size: 20),
                      ),
                      errorWidget: (context, url, error) => Container(
                        width: 60,
                        height: 60,
                        color: Colors.grey[200],
                        child: Icon(Icons.error, color: Colors.red, size: 20),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// Enhanced input area with modern styling
  Widget _buildEnhancedInputArea(
    BuildContext context,
    ThemeData theme,
    bool isDark,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
        border: Border(
          top: BorderSide(
            color: isDark ? AppTheme.darkOutline : AppTheme.lightOutline,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Input Row
              Container(
                decoration: BoxDecoration(
                  color: theme.cardColor,
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // File attachment button
                    Container(
                      margin: EdgeInsets.only(left: 8),
                      child: IconButton(
                        icon: Icon(
                          Icons.attach_file,
                          color: AppTheme.primaryGreen,
                        ),
                        tooltip: 'Attach files',
                        onPressed: () {
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            builder: (BuildContext context) {
                              return Container(
                                decoration: BoxDecoration(
                                  color: theme.scaffoldBackgroundColor,
                                  borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(20),
                                  ),
                                ),
                                child: FileSelectionActionGrid(
                                  onFilesSelected: _handleFileUrls,
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ),

                    // Audio recorder (if supported)
                    if (kIsWeb ||
                        (!kIsWeb && !(Platform.isMacOS && kDebugMode)))
                      AudioRecorderWidget(
                        onTranscriptionComplete: _handleTranscription,
                      ),

                    // Text input field
                    Expanded(
                      child: Container(
                        margin: EdgeInsets.symmetric(horizontal: 8),
                        child: TextFormField(
                          maxLines: null,
                          minLines: 1,
                          key: Key("QuestionInputTextField"),
                          controller: _textController,
                          onChanged: (value) {
                            _updateWordAndLetterCount(value);
                          },
                          onFieldSubmitted: (value) async {
                            await onSendPressed(context);
                          },
                          decoration: InputDecoration(
                            hintText: "Type your message...",
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            hintStyle: TextStyle(color: theme.hintColor),
                          ),
                          style: theme.textTheme.bodyLarge,
                        ),
                      ),
                    ),

                    // Send button or loading indicator
                    Container(
                      margin: EdgeInsets.only(right: 8),
                      child: _isLoading
                          ? Container(
                              width: 40,
                              height: 40,
                              padding: EdgeInsets.all(8),
                              child: LoadingStyles.primary(size: 24),
                            )
                          : IconButton(
                              key: Key("SendInputQuestionButton"),
                              icon: Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppTheme.primaryGreen,
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.send,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                              tooltip: 'Send message',
                              onPressed: () async {
                                await onSendPressed(context);
                              },
                            ),
                    ),

                    // Voice chat button (debug mode only)
                    // TODO: real time api too expensive, let's keep it debug mode for now
                    if (kDebugMode)
                      Container(
                        margin: EdgeInsets.only(right: 8),
                        child: IconButton(
                          onPressed: () {
                            // Navigate to the VoiceChatWidget page
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => VoiceChatWidget(),
                              ),
                            );
                          },
                          icon: Icon(
                            Icons.mic_none_rounded,
                            color: AppTheme.primaryGreen,
                          ),
                          tooltip: 'Voice chat (Debug)',
                        ),
                      ),
                  ],
                ),
              ),

              // Word and letter count
              if (_wordCount > 0 || _letterCount > 0)
                Container(
                  margin: EdgeInsets.only(top: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Words: $_wordCount',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                      SizedBox(width: 16),
                      Text(
                        'Characters: $_letterCount',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
