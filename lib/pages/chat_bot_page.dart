import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:diogeneschatbot/controllers/chat_bot_controller.dart';
import 'package:diogeneschatbot/models/bot.dart';
import 'package:diogeneschatbot/models/conversation.dart';
import 'package:diogeneschatbot/models/profile.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/util/functions.dart';
import 'package:diogeneschatbot/util/util_token.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/widgets/chat_bot_conversation_stream_widget.dart';
import 'package:diogeneschatbot/widgets/chat_bot_message_input_widget.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

// TODO: Add message search functionality
// TODO: Implement chat export/backup feature
// TODO: Add conversation templates for quick starts
// TODO: Implement message reactions and interactions
// TODO: Add typing indicators and read receipts
// TODO: Implement chat themes and customization options

/// Enhanced bot chat page with modern UI and improved user experience
class BotChatPage extends StatefulWidget {
  final Bot selectedBot;
  final String? userId;
  final String? conversationId;

  const BotChatPage({
    super.key,
    required this.selectedBot,
    this.userId,
    this.conversationId,
  });

  @override
  State<BotChatPage> createState() => _BotChatPageState();
}

class _BotChatPageState extends State<BotChatPage> with WidgetsBindingObserver {
  Timer? _debounceTimer;
  int _wordCount = 0;
  int _letterCount = 0;
  List<BotChatMessage> messages = [];
  TextEditingController messageController = TextEditingController();
  ConversationRepository _conversationRepository = ConversationRepository();
  ProfileRepository _profileRepository = ProfileRepository();
  final ScrollController _scrollController = ScrollController();
  final StreamController<String> _streamController =
      StreamController<String>.broadcast();
  StreamSubscription? _subscription;

  CollectionReference<Map<String, dynamic>> conversationsRef = FirebaseFirestore
      .instance
      .collection('conversations');

  String? conversationId;
  Stream<QuerySnapshot<Map<String, dynamic>>>? conversationStream;
  bool _loading = false;
  bool _conversationClosed = false;
  final String? serverHost =
      dotenv.env['EMBEDDING_SERVER_HOST'] ??
      const String.fromEnvironment("EMBEDDING_SERVER_HOST");
  final String? diogenesWebUrl =
      dotenv.env['DIOGENES_WEB_URL'] ??
      const String.fromEnvironment("DIOGENES_WEB_URL");

  late ChatBotController _chatController;

  // Enhanced: Local user ID management since widget.userId is final
  String? _currentUserId;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _currentUserId = widget.userId; // Initialize local user ID
    _chatController = ChatBotController(
      serverHost: serverHost,
      subscription: _subscription,
      streamController: _streamController,
      setLoading: setLoading,
    );
    _initConversation();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _streamController.close();
    _subscription?.cancel();
    super.dispose();
  }

  Future<void> _initConversation() async {
    if (_currentUserId == null || _currentUserId!.isEmpty) {
      await _signInAnonymously();
    }
    if (widget.conversationId != null) {
      await _loadExistingConversation();
    } else {
      await _createNewConversation();
    }
  }

  Future<void> _loadExistingConversation() async {
    conversationId = widget.conversationId;
    Conversation? newConversation = await _conversationRepository
        .getConversationById(conversationId!);
    _conversationClosed = newConversation?.isConversationClosed ?? false;
    _setUpConversationStream();
  }

  Future<void> _createNewConversation() async {
    Query query = _conversationRepository.getConversationQueryByBotId(
      widget.selectedBot.id,
    );

    if (_currentUserId != null && _currentUserId!.isNotEmpty) {
      query = query.where('userId', isEqualTo: _currentUserId);
    } else {
      query = query.where('userId', isNull: true);
    }

    query = query.where("isConversationClosed", isEqualTo: false);
    final querySnapshot = await query.get();

    if (querySnapshot.docs.isEmpty) {
      final newConversationId = await _conversationRepository
          .createConversation(
            Conversation(
              id: '',
              userId: _currentUserId?.isEmpty ?? true ? null : _currentUserId,
              botId: widget.selectedBot.id,
              messages: [],
              createdAt: DateTime.now().toUtc(),
            ),
          );

      conversationId = newConversationId;
    } else {
      conversationId = querySnapshot.docs.first.id;
      Conversation? newConversation = await _conversationRepository
          .getConversationById(conversationId!);
      _conversationClosed = newConversation?.isConversationClosed ?? false;
    }

    _setUpConversationStream();
  }

  Future<void> _signInAnonymously() async {
    UserCredential userCredential = await FirebaseAuth.instance
        .signInAnonymously();
    _currentUserId = userCredential.user!.uid; // Use local variable
    logger.d("user sign in anonymously");
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (kIsWeb && state == AppLifecycleState.paused) {
      _closeConversation();
    }
    super.didChangeAppLifecycleState(state);
  }

  Future<void> _closeConversation() async {
    if (conversationId != null && !_conversationClosed) {
      setState(() {
        _conversationClosed = true;
      });

      await conversationsRef.doc(conversationId).update({'closed': true});
      await _shareConversation();
    }
  }

  Future<void> _shareConversation() async {
    final link = '$diogenesWebUrl/conversation?conversationId=$conversationId';
    logger.d('Sharable link: $link');

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: _conversationClosed
              ? Text('Conversation closed')
              : Text("Share this conversation"),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _conversationClosed
                  ? Text('This conversation is now closed.')
                  : SizedBox.shrink(),
              SizedBox(height: 16),
              Text('Share this conversation using the link below:'),
              SizedBox(height: 8),
              SelectableText(link, style: TextStyle(color: Colors.blue)),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);
                await Clipboard.setData(ClipboardData(text: link));
                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(content: Text('Link copied to clipboard')),
                  );
                  navigator.pop();
                }
              },
              child: Text('Copy Link'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('OK'),
            ),
          ],
        );
      },
    );
  }

  void _setUpConversationStream() {
    conversationStream = _conversationRepository.getConversationStream(
      conversationId!,
    );
    setState(() {});
  }

  void _handleTranscription(String transcript) {
    logger.d('Transcribed text: $transcript');
    setState(() {
      messageController.text = transcript;
      _updateWordAndLetterCount(transcript);
    });
  }

  void _updateWordAndLetterCount(String value) {
    if (_debounceTimer != null && _debounceTimer!.isActive) {
      _debounceTimer!.cancel();
    }

    _debounceTimer = Timer(Duration(milliseconds: 500), () {
      if (value.isNotEmpty && value.endsWith(' ')) {
        setState(() {
          _wordCount = Util_Token.countTokens(value.trim());
          _letterCount = value.trim().length;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
      appBar: AppBarStyles.primary(
        title: _conversationClosed
            ? '${widget.selectedBot.name} (Closed)'
            : widget.selectedBot.name,
        actions: [
          // Enhanced: Add more action buttons with tooltips
          IconButton(
            icon: Icon(Icons.info_outline, color: Colors.white),
            tooltip: 'Bot Information',
            onPressed: () => _showBotInfo(context),
          ),
          IconButton(
            icon: Icon(Icons.close, color: Colors.white),
            tooltip: 'Close Conversation',
            onPressed: _closeConversation,
          ),
          IconButton(
            icon: Icon(Icons.share, color: Colors.white),
            tooltip: 'Share Conversation',
            onPressed: _shareConversation,
          ),
        ],
      ),
      body: conversationStream != null
          ? Column(
              children: [
                Expanded(
                  child: conversationId != null
                      ? ConversationStream(
                          conversationStream: conversationStream!,
                          userId: _currentUserId,
                          selectedBot: widget.selectedBot,
                          profileRepository: _profileRepository,
                          scrollController: _scrollController,
                          streamController: _streamController,
                          loading: _loading,
                          onLoadingChanged: (loading) {
                            setState(() {
                              _loading = loading;
                            });
                          },
                        )
                      : Center(child: CircularProgressIndicator()),
                ),
                !_conversationClosed
                    ? MessageInput(
                        messageController: messageController,
                        onSendPressed: onSendPressed,
                        onTranscriptionComplete: _handleTranscription,
                        loading: _loading,
                        wordCount: _wordCount,
                        letterCount: _letterCount,
                        onWordAndLetterCountChanged: _updateWordAndLetterCount,
                      )
                    : SizedBox.shrink(),
              ],
            )
          : Center(child: CircularProgressIndicator()),
    );
  }

  Future<void> onSendPressed() async {
    setState(() {
      _wordCount = 0;
      _letterCount = 0;
    });
    String value = messageController.text;
    messageController.clear();
    await _sendMessageStreaming(value);
  }

  Future<void> _sendMessageStreaming(String text) async {
    setLoading(true);

    if (_subscription != null) {
      await _subscription!.cancel();
      _subscription = null;
    }

    if (widget.selectedBot.agentName == null ||
        widget.selectedBot.agentName!.isEmpty) {
      await _handleUserMessage(text);
    } else {
      await _handleAgentMessage(text);
    }

    messageController.clear();
  }

  void setLoading(bool value) {
    setState(() {
      _loading = value;
    });
  }

  Future<void> _handleUserMessage(String text) async {
    final message = BotChatMessage(
      text: text,
      senderId: _currentUserId ?? "",
      timestamp: Timestamp.now(),
    );

    await _conversationRepository.addMessageToConversation(
      conversationId!,
      message,
    );

    String responseTotal = await _chatController.sendRequestToServer(
      text,
      widget.selectedBot,
      _currentUserId,
      conversationId,
    );
    _streamController.add(Functions.filterString(responseTotal));
  }

  Future<void> _handleAgentMessage(String text) async {
    await _chatController.chatWithAgentStreaming(
      botId: widget.selectedBot.id,
      userId: _currentUserId!,
      conversationId: conversationId!,
      userInput: text,
    );
  }

  /// Shows bot information dialog with enhanced UI
  void _showBotInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.smart_toy, color: AppTheme.primaryGreen),
            SizedBox(width: 8),
            Text('Bot Information'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('Name', widget.selectedBot.name),
            if (widget.selectedBot.description.isNotEmpty)
              _buildInfoRow('Description', widget.selectedBot.description),
            if (widget.selectedBot.agentName?.isNotEmpty == true)
              _buildInfoRow('Agent', widget.selectedBot.agentName!),
            _buildInfoRow('Bot ID', widget.selectedBot.id),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Builds information row for bot info dialog
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
