import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:diogeneschatbot/models/apiusagemodel.dart';
import 'package:diogeneschatbot/models/usage.dart';
import 'package:diogeneschatbot/pages/create_post_page.dart';
import 'package:diogeneschatbot/services/apiusage_service.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/util/util.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';

import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:diogeneschatbot/widgets/tts_button.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// import 'package:flutter_animate/flutter_animate.dart';
import 'package:readmore/readmore.dart';
import 'package:grouped_list/grouped_list.dart';

class ChatHistoryPage extends StatefulWidget {
  final Usage usage;

  const ChatHistoryPage({super.key, required this.usage});

  @override
  _ChatHistoryPageState createState() => _ChatHistoryPageState();
}

class _ChatHistoryPageState extends State<ChatHistoryPage> {
  List<ApiUsage> _chatHistory = [];
  List<ApiUsage> _filteredChatHistory = [];
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  String _searchQuery = '';

  final TextEditingController _searchController = TextEditingController();
  final String userUUID = Util.getUserID();

  @override
  void initState() {
    super.initState();
    _loadChatHistory();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadChatHistory() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      List<ApiUsage> chatHistoryString = await ApiUsageService()
          .getApiUsageForUserAndType(userUUID, widget.usage.type.toString());

      setState(() {
        _chatHistory = chatHistoryString;
        _filteredChatHistory = chatHistoryString;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Failed to load chat history: ${e.toString()}';
      });
    }
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredChatHistory = _chatHistory;
      } else {
        _filteredChatHistory = _chatHistory.where((chat) {
          final question = chat.question?.toLowerCase() ?? '';
          final answer = chat.answer?.toLowerCase() ?? '';
          return question.contains(query) || answer.contains(query);
        }).toList();
      }
    });
  }

  Future<void> _refreshChatHistory() async {
    await _loadChatHistory();
  }

  Widget _buildErrorState() {
    return Center(
      child: EnhancedCard(
        margin: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Oops! Something went wrong',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _refreshChatHistory,
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final isFiltered = _searchQuery.isNotEmpty && _filteredChatHistory.isEmpty;

    return Center(
      child: EnhancedCard(
        margin: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isFiltered ? Icons.search_off : Icons.chat_bubble_outline,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              isFiltered ? 'No results found' : 'No chat history yet',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              isFiltered
                  ? 'Try adjusting your search terms'
                  : 'Start a conversation to see your chat history here',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            if (isFiltered) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () => _searchController.clear(),
                icon: const Icon(Icons.clear),
                label: const Text('Clear Search'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBarStyles.primary(
        title: 'Chat History - ${widget.usage.type.toString().split('.').last}',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshChatHistory,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: isDark ? AppTheme.darkGradient : AppTheme.lightGradient,
        ),
        child: Column(
          children: [
            // Search bar
            Container(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search chat history...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: theme.colorScheme.surface.withValues(alpha: 0.9),
                ),
              ),
            ),
            // Content area
            Expanded(child: _buildContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: EnhancedLoading(
          type: LoadingType.pulse,
          message: 'Loading chat history...',
          showMessage: true,
        ),
      );
    }

    if (_hasError) {
      return _buildErrorState();
    }

    if (_filteredChatHistory.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _refreshChatHistory,
      child: GroupedListView<ApiUsage, DateTime>(
        elements: _filteredChatHistory,
        groupBy: (ApiUsage chatMessage) => DateTime(
          chatMessage.date.year,
          chatMessage.date.month,
          chatMessage.date.day,
        ),
        groupComparator: (DateTime date1, DateTime date2) =>
            date2.compareTo(date1),
        itemComparator: (ApiUsage chat1, ApiUsage chat2) =>
            chat2.date.compareTo(chat1.date),
        order: GroupedListOrder.ASC,
        groupSeparatorBuilder: (DateTime groupByValue) => Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: AppTheme.primaryGreen.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: AppTheme.primaryGreen.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.calendar_today,
                size: 16,
                color: AppTheme.primaryGreen,
              ),
              const SizedBox(width: 8),
              Text(
                '${groupByValue.day}/${groupByValue.month}/${groupByValue.year}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryGreen,
                ),
              ),
            ],
          ),
        ),
        itemBuilder: (context, ApiUsage chatMessage) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: EnhancedCard(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Timestamp
                  Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        size: 16,
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        chatMessage.date.toLocal().toString().substring(0, 19),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Question section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.accentBlue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppTheme.accentBlue.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.help_outline,
                              size: 16,
                              color: AppTheme.accentBlue,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Question',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: AppTheme.accentBlue,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        ReadMoreText(
                          chatMessage.question!.trim(),
                          trimLines: 3,
                          style: TextStyle(
                            color: AppTheme.accentBlue,
                            fontSize: 14,
                          ),
                          colorClickableText: AppTheme.accentPink,
                          trimMode: TrimMode.Line,
                          trimCollapsedText: ' Show more',
                          trimExpandedText: ' Show less',
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),
                  // Media attachments
                  if (chatMessage.imageUrls != null &&
                          chatMessage.imageUrls!.isNotEmpty ||
                      chatMessage.audioUrls != null &&
                          chatMessage.audioUrls!.isNotEmpty ||
                      chatMessage.textUrls != null &&
                          chatMessage.textUrls!.isNotEmpty ||
                      chatMessage.videoUrls != null &&
                          chatMessage.videoUrls!.isNotEmpty) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryGreen.withValues(alpha: 0.05),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppTheme.primaryGreen.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.attachment,
                                size: 16,
                                color: AppTheme.primaryGreen,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Attachments',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: AppTheme.primaryGreen,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          // Images
                          if (chatMessage.imageUrls != null &&
                              chatMessage.imageUrls!.isNotEmpty)
                            Wrap(
                              spacing: 8.0,
                              runSpacing: 8.0,
                              children: chatMessage.imageUrls!
                                  .map(
                                    (imageUrl) => ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: SizedBox(
                                        width: 80,
                                        height: 80,
                                        child: CachedNetworkImage(
                                          imageUrl: imageUrl,
                                          fit: BoxFit.cover,
                                          placeholder: (context, url) =>
                                              Container(
                                                color: Colors.grey[300],
                                                child: const Icon(Icons.image),
                                              ),
                                          errorWidget: (context, url, error) =>
                                              Container(
                                                color: Colors.grey[300],
                                                child: const Icon(Icons.error),
                                              ),
                                        ),
                                      ),
                                    ),
                                  )
                                  .toList(),
                            ),
                          // Other media indicators
                          if (chatMessage.audioUrls != null &&
                              chatMessage.audioUrls!.isNotEmpty)
                            Chip(
                              avatar: const Icon(Icons.audiotrack, size: 16),
                              label: const Text('Audio available'),
                              backgroundColor: AppTheme.accentCoral.withValues(
                                alpha: 0.2,
                              ),
                            ),
                          if (chatMessage.textUrls != null &&
                              chatMessage.textUrls!.isNotEmpty)
                            Chip(
                              avatar: const Icon(Icons.text_snippet, size: 16),
                              label: const Text('Text available'),
                              backgroundColor: AppTheme.accentPink.withValues(
                                alpha: 0.2,
                              ),
                            ),
                          if (chatMessage.videoUrls != null &&
                              chatMessage.videoUrls!.isNotEmpty)
                            Chip(
                              avatar: const Icon(Icons.videocam, size: 16),
                              label: const Text('Video available'),
                              backgroundColor: AppTheme.secondaryGreen
                                  .withValues(alpha: 0.2),
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                  ],
                  // Answer section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppTheme.primaryGreen.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              size: 16,
                              color: AppTheme.primaryGreen,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Answer',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: AppTheme.primaryGreen,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        widget.usage.type.toString() !=
                                UsageType.generateImage.toString()
                            ? ReadMoreText(
                                chatMessage.answer!.trim(),
                                trimLines: 3,
                                style: TextStyle(
                                  color: AppTheme.primaryGreen,
                                  fontSize: 14,
                                ),
                                colorClickableText: AppTheme.accentPink,
                                trimMode: TrimMode.Line,
                                trimCollapsedText: ' Show more',
                                trimExpandedText: ' Show less',
                              )
                            : ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: CachedNetworkImage(
                                  imageUrl: chatMessage.answer!.trim(),
                                  fit: BoxFit.contain,
                                  placeholder: (context, url) => Container(
                                    height: 200,
                                    color: Colors.grey[300],
                                    child: const Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) =>
                                      Container(
                                        height: 200,
                                        color: Colors.grey[300],
                                        child: const Center(
                                          child: Icon(Icons.error),
                                        ),
                                      ),
                                ),
                              ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Action buttons
                  Row(
                    children: [
                      // TTS Button
                      TTSButton(textToSpeak: chatMessage.answer!),

                      // Copy button
                      IconButton(
                        icon: const Icon(Icons.copy_rounded),
                        onPressed: () {
                          Clipboard.setData(
                            ClipboardData(text: chatMessage.answer!),
                          );
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: const Text("Copied to clipboard"),
                              duration: const Duration(seconds: 2),
                              backgroundColor: AppTheme.primaryGreen,
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                      ),

                      // Share button (only on supported platforms)
                      if (kIsWeb || (!Platform.isIOS && !Platform.isMacOS))
                        IconButton(
                          icon: const Icon(Icons.share_rounded),
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => CreatePostPage(
                                  userId: userUUID,
                                  initialText:
                                      widget.usage.type.toString() !=
                                          UsageType.generateImage.toString()
                                      ? 'Question: ${chatMessage.question!}\n\nAnswer: ${chatMessage.answer!}'
                                      : "Question: ${chatMessage.question!}",
                                  initialMedialUrls:
                                      widget.usage.type.toString() !=
                                          UsageType.generateImage.toString()
                                      ? []
                                      : [chatMessage.answer!],
                                ),
                              ),
                            );
                          },
                          tooltip: 'Share as post',
                        ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
