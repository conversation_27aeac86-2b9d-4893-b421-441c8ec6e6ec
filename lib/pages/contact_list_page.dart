import 'package:diogeneschatbot/models/chat_room.dart';
import 'package:diogeneschatbot/models/profile.dart';
import 'package:diogeneschatbot/pages/chat_friends_page.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/widgets/avatar_widget.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:flutter/material.dart';

class ContactListScreen extends StatefulWidget {
  final String userId;
  final String currentUserId;
  final bool showAppBar;
  final String title;
  final bool isUsedForSelection;

  //Status to filter, could be confirmed, pending, blocked, blocking
  Set<String> statuses = <String>{};

  ContactListScreen({
    required this.userId,
    required this.currentUserId,
    required this.showAppBar,
    this.title = "Contacts",
    this.isUsedForSelection = false,
    Set<String>? statuses,
  }) : statuses = statuses ?? {"confirmed"};

  @override
  _ContactListScreenState createState() => _ContactListScreenState();
}

class _ContactListScreenState extends State<ContactListScreen> {
  ChatRepository chatRepository = ChatRepository();
  ProfileRepository profileRepository = ProfileRepository();
  List<String> selectedContacts = [];

  Future<void> _navigateToChatScreen(
    BuildContext context,
    ChatRoom chatRoom,
  ) async {
    // Create the chat room if it doesn't exist
    await chatRepository.createChatRoom(chatRoom);

    // Navigate to the chat screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatFriendsPage(
          chatRoomId: chatRoom.chatRoomId,
          memberIds: chatRoom.memberIds,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Profile?>(
      future: profileRepository.getProfile(widget.userId),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          logger.d('Error: ${snapshot.error}');
          return Text('Error: ${snapshot.error}');
        }

        if (!snapshot.hasData) {
          return CircularProgressIndicator();
        }

        final Set<Contact> contactsList = snapshot.data?.contacts ?? {};
        // build a map between contact id and contact object
        final Map<String, Contact> contactsMap = {
          for (var contact in contactsList) contact.id: contact,
        };

        // we only show "confirmed" status contact
        List<String> contactIds = contactsList
            .where((contact) => widget.statuses.contains(contact.status))
            .map((contactMap) => (contactMap).id)
            .toList();

        return Material(
          child: Scaffold(
            appBar: widget.showAppBar
                ? AppBar(
                    title: Text(widget.title),
                    leading: IconButton(
                      icon: Icon(Icons.arrow_back),
                      onPressed: () {
                        Navigator.pop(
                          context,
                        ); // Navigate back to the previous screen
                      },
                    ),
                  )
                : null,
            body: FutureBuilder<List<Profile?>>(
              future: profileRepository.getProfiles(contactIds),
              builder: (context, contactSnapshot) {
                if (snapshot.hasError) {
                  logger.d('Error: ${snapshot.error}');
                  return Text('Error: ${snapshot.error}');
                }

                if (!contactSnapshot.hasData) {
                  return CircularProgressIndicator();
                }

                final List<Profile?> contactInfos = contactSnapshot.data!;
                return ListView.builder(
                  itemCount: contactIds.length,
                  itemBuilder: (context, index) {
                    final Profile? contactInfo = contactInfos[index];

                    return contactInfo == null
                        ? SizedBox.shrink()
                        : Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.green, width: 1),
                              borderRadius: BorderRadius.circular(15.0),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: ListTile(
                                    leading: AvatarWidget(profile: contactInfo),
                                    title: Text(contactInfo.name ?? ''),
                                    subtitle: Text(contactInfo.username ?? ''),
                                    onTap: () async {
                                      List<String> memberIds = [
                                        widget.currentUserId,
                                        contactInfo.id,
                                      ];

                                      // Compute the chat room ID based on the selected users
                                      String chatRoomId =
                                          ChatRepository.generateChatRoomId(
                                            memberIds,
                                          );

                                      // Create the ChatRoom object
                                      ChatRoom chatRoom = ChatRoom(
                                        chatRoomId: chatRoomId,
                                        memberIds: memberIds,
                                      );

                                      // Create chat room (if it doesn't exist) and navigate to the ChatScreen
                                      await _navigateToChatScreen(
                                        context,
                                        chatRoom,
                                      );
                                    },
                                    // only allow delete when currentUserId == widget.userId
                                    // check status of it
                                    trailing:
                                        widget.currentUserId == widget.userId
                                        ? ContactActionWidget(
                                            contactsMap: contactsMap,
                                            contactInfo: contactInfo,
                                            profileRepository:
                                                profileRepository,
                                            widget: widget,
                                          )
                                        : SizedBox.shrink(),
                                  ),
                                ),
                                widget.isUsedForSelection
                                    ? Checkbox(
                                        value: selectedContacts.contains(
                                          contactInfo.id,
                                        ),
                                        onChanged: (bool? value) {
                                          setState(() {
                                            if (value == true) {
                                              selectedContacts.add(
                                                contactInfo.id,
                                              );
                                            } else {
                                              selectedContacts.remove(
                                                contactInfo.id,
                                              );
                                            }
                                          });
                                        },
                                      )
                                    : SizedBox.shrink(),
                              ],
                            ),
                          );
                  },
                );
              },
            ),
            floatingActionButton: widget.isUsedForSelection
                ? FloatingActionButton(
                    onPressed: () {
                      Navigator.pop(context, selectedContacts);
                    },
                    child: Icon(Icons.check),
                  )
                : null,
          ),
        );
      },
    );
  }
}

class ContactActionWidget extends StatelessWidget {
  const ContactActionWidget({
    super.key,
    required this.contactsMap,
    required this.contactInfo,
    required this.profileRepository,
    required this.widget,
  });

  final Map<String, Contact> contactsMap;
  final Profile contactInfo;
  final ProfileRepository profileRepository;
  final ContactListScreen widget;

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        switch (contactsMap[contactInfo.id]!.status) {
          case "confirmed":
            return IconButton(
              icon: Icon(Icons.delete),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return AlertDialog(
                      title: Text('Confirm Delete'),
                      content: Text('Are you sure you want to delete?'),
                      actions: [
                        TextButton(
                          child: Text('Cancel'),
                          onPressed: () {
                            // Close the dialog without deleting the conversation
                            Navigator.of(context).pop();
                          },
                        ),
                        TextButton(
                          child: Text('Delete'),
                          onPressed: () async {
                            await profileRepository.removeContact(
                              widget.currentUserId,
                              contactInfo.id,
                            );
                            Navigator.of(context).pop();
                          },
                        ),
                      ],
                    );
                  },
                );
              },
            );
          case "pending":
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                IconButton(
                  icon: Icon(Icons.check),
                  onPressed: () =>
                      profileRepository.approveRequest(contactInfo.id),
                ),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () =>
                      profileRepository.rejectRequest(contactInfo.id),
                ),
              ],
            );
          case "blocked":
            //  Add  button for the "blocked" case here
            return IconButton(
              icon: Icon(Icons.lock_open),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return AlertDialog(
                      title: Text('Confirm Unblock'),
                      content: Text(
                        'Are you sure you want to unblock this contact?',
                      ),
                      actions: [
                        TextButton(
                          child: Text('Cancel'),
                          onPressed: () {
                            // Close the dialog without deleting the conversation
                            Navigator.of(context).pop();
                          },
                        ),
                        TextButton(
                          child: Text('Unblock'),
                          onPressed: () async {
                            await profileRepository.unblockContact(
                              widget.currentUserId,
                              contactInfo.id,
                            );
                            Navigator.of(context).pop();
                          },
                        ),
                      ],
                    );
                  },
                );
              },
            );
          default:
            return SizedBox.shrink();
        }
      },
    );
  }
}
