import 'dart:async';
import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/view.dart';
import 'package:diogeneschatbot/models/usage.dart';
import 'package:diogeneschatbot/pages/chathistory_page.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/util/util.dart';
import 'package:diogeneschatbot/util/util_token.dart';
import 'package:diogeneschatbot/utils/image_utils.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/widgets/chat_message_widgets.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';

import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

// TODO: Add image gallery widget for better image browsing experience
// TODO: Implement image favorites/bookmarking system
// TODO: Add image metadata display (size, format, generation time)
// TODO: Implement image sharing functionality
// TODO: Add image editing tools (crop, resize, filters)

/// Enhanced image generation page for creating AI-generated images
/// Provides comprehensive image generation, editing, and variation capabilities
class GenerateImagePage extends StatefulWidget {
  /// Page title displayed in the app bar
  final String title;

  /// Usage type for tracking and billing purposes
  final Usage type;

  /// Current user ID for authentication and tracking
  final String currentUserId;

  const GenerateImagePage({
    super.key,
    required this.title,
    required this.type,
    required this.currentUserId,
  });

  @override
  State<GenerateImagePage> createState() => _GenerateImagePageState();
}

/// Available image operations for enhanced functionality
enum ImageOperation {
  /// Edit an existing image with AI
  editImage,

  /// Create variations of an existing image
  createVariations,
}

class _GenerateImagePageState extends State<GenerateImagePage> {
  // Core state
  String _inputText = '';
  String _imageUrl = '';
  DateTime _requestDateTime = DateTime.fromMillisecondsSinceEpoch(0);

  // Counters and metrics
  int _wordCount = 0;
  int _letterCount = 0;

  // Image handling
  File? _image;
  File? _mask;
  final ImagePicker _picker = ImagePicker();

  // Chat and UI state
  List<Widget> _chatMessages = [];
  ImageOperation? _selectedOperation;
  bool _isLoading = false;

  // Controllers and streams
  final TextEditingController _textController = TextEditingController();
  final StreamController<List<Widget>> _chatStreamController =
      StreamController<List<Widget>>.broadcast();

  // Enhanced: Focus node for better input handling
  final FocusNode _inputFocusNode = FocusNode();

  /// Get the usage type from widget
  Usage get usageType => widget.type;

  @override
  void initState() {
    super.initState();
    _chatStreamController.add(_chatMessages);
  }

  @override
  void dispose() {
    _chatStreamController.close();
    _textController.dispose();
    _inputFocusNode.dispose();
    super.dispose();
  }

  /// Picks an image from the gallery for editing or variations
  Future<void> _pickImage() async {
    final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      final File? processedImage = await ImageUtils.processImage(
        File(pickedFile.path),
      );
      if (processedImage != null) {
        setState(() {
          _image = processedImage;
        });
      } else {
        logger.d('Invalid image.');
      }
    } else {
      logger.d('No image selected.');
    }
  }

  Future<void> _editImage() async {
    if (_image == null) {
      _showSnackBar('Please upload an image first.');
      return;
    }
    setState(() {
      _isLoading = true;
    });
    final editedImageUrl = await Util.editImage(
      _image!,
      _mask,
      _textController.text,
      widget.currentUserId,
      usageType,
      widget.currentUserId,
    );
    setState(() {
      _imageUrl = editedImageUrl;
      _isLoading = false;
      _requestDateTime = DateTime.now().toUtc();
      _updateChatMessages();
    });
  }

  Future<void> _createVariations() async {
    if (_image == null) {
      _showSnackBar('Please upload an image first.');
      return;
    }
    setState(() {
      _isLoading = true;
    });
    final variations = await Util.createImageVariations(
      _image!,
      widget.currentUserId,
      usageType,
      widget.currentUserId,
    );
    setState(() {
      _imageUrl = variations;
      _isLoading = false;
      _requestDateTime = DateTime.now().toUtc();
      _updateChatMessages();
    });
  }

  Future<void> _sendMessage(String message, Usage usageType) async {
    BotToast.showText(
      text: "Please be patient while image generation is in progress...",
      duration: Duration(seconds: 3),
    ); // Show explicit message

    BotToast.showLoading(); // Show loading toast
    setState(() {
      _isLoading = true;
    });
    final String inputText = _textController.text;
    final response = await Util.CallChatAPI(
      inputText,
      usageType,
      <Map<String, String>>[],
      widget.currentUserId,
      widget.currentUserId,
    );
    final imageUrlRegExp = RegExp(
      r'^https?:\/\/[\w-]+(\.[\w-]+)+([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?\.(jpg|jpeg|png|gif|bmp)(\?.*)?$',
    );
    final isValidImageUrl = imageUrlRegExp.hasMatch(response.trim());
    if (isValidImageUrl) {
      setState(() {
        _inputText = inputText;
        _imageUrl = response;
        _requestDateTime = DateTime.now().toUtc();
        _isLoading = false;
        _updateChatMessages();
      });
    } else {
      logger.d('Request failed.');
      setState(() {
        _isLoading = false;
      });
    }
    BotToast.closeAllLoading(); // Show loading toast
  }

  List<Widget> _createChatMessage() {
    return <Widget>[
      if (_requestDateTime != DateTime.fromMillisecondsSinceEpoch(0)) ...[
        Column(
          children: [
            ChatMessageHeader(requestDateTime: _requestDateTime),
            ChatMessageBody(inputText: _inputText),
            ChatMessageImage(imageUrl: _imageUrl),
            ChatMessageActions(
              imageUrl: _imageUrl,
              inputText: _inputText,
              currentUserId: widget.currentUserId,
            ),
          ],
        ),
      ],
    ];
  }

  void _updateChatMessages() {
    setState(() {
      _chatMessages.addAll(_createChatMessage());
    });
    _chatStreamController.sink.add(_chatMessages);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
      appBar: AppBarStyles.primary(
        title: 'AI Image Generator',
        actions: [
          // Chat History
          IconButton(
            key: Key("ChatHistoryButtonOnChatPage"),
            icon: Icon(Icons.history, color: Colors.white),
            tooltip: 'Image Generation History',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ChatHistoryPage(usage: usageType),
                ),
              );
            },
          ),
          // Offline Generation (iOS/macOS only)
          if (!kIsWeb && (Platform.isIOS || Platform.isMacOS))
            IconButton(
              key: Key("GenerateImageButtonOfflinePage"),
              icon: Icon(Icons.offline_bolt_outlined, color: Colors.white),
              tooltip: 'Offline Image Generation',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => OfflineImageGenerationPage(),
                  ),
                );
              },
            ),
        ],
      ),
      body: Column(
        children: <Widget>[
          // Enhanced chat area
          Expanded(
            child: _chatMessages.isEmpty
                ? _buildEmptyState(context, theme)
                : StreamBuilder(
                    stream: _chatStreamController.stream,
                    initialData: _chatMessages,
                    builder: (BuildContext context, AsyncSnapshot snapshot) {
                      return ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: snapshot.data.length,
                        reverse: true,
                        itemBuilder: (BuildContext context, int index) {
                          return snapshot.data[snapshot.data.length -
                              1 -
                              index];
                        },
                      );
                    },
                  ),
          ),

          // Enhanced image thumbnails and controls
          if (_image != null || _mask != null) _buildEnhancedImageThumbnails(),

          // Enhanced input area
          _buildEnhancedInputArea(context, theme, isDark),
        ],
      ),
    );
  }

  // TODO: Removed unused methods - functionality moved to enhanced input area

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  /// Builds an enhanced empty state widget with helpful messaging and tips
  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Main welcome card
          CardStyles.outlined(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Animated icon with glow effect
                TweenAnimationBuilder<double>(
                  duration: const Duration(seconds: 2),
                  tween: Tween(begin: 0.5, end: 1.0),
                  builder: (context, value, child) {
                    return Transform.scale(
                      scale: value,
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.primaryGreen.withValues(
                                alpha: 0.3,
                              ),
                              blurRadius: 20 * value,
                              spreadRadius: 5 * value,
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.auto_awesome,
                          size: 64,
                          color: AppTheme.primaryGreen,
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 16),
                Text(
                  'AI Image Generator',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: AppTheme.primaryGreen,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Transform your imagination into stunning visuals with AI-powered image generation.',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.hintColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () {
                    _inputFocusNode.requestFocus();
                  },
                  icon: Icon(Icons.create, color: Colors.white),
                  label: Text(
                    'Start Creating',
                    style: TextStyle(color: Colors.white),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryGreen,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Tips and examples section
          _buildTipsSection(theme),

          // Feature highlights
          _buildFeatureHighlights(theme),
        ],
      ),
    );
  }

  /// Builds helpful tips section for users
  Widget _buildTipsSection(ThemeData theme) {
    final tips = [
      'Be specific: "A red sports car on a mountain road at sunset"',
      'Include style: "Digital art", "Photorealistic", "Watercolor"',
      'Add mood: "Peaceful", "Dramatic", "Vibrant", "Mysterious"',
      'Specify details: Colors, lighting, composition, perspective',
    ];

    return CardStyles.outlined(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb_outline, color: AppTheme.secondaryGreen),
              const SizedBox(width: 8),
              Text(
                'Pro Tips for Better Results',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.secondaryGreen,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...tips.map(
            (tip) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    size: 16,
                    color: AppTheme.primaryGreen,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      tip,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.hintColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds feature highlights section
  Widget _buildFeatureHighlights(ThemeData theme) {
    final features = [
      {
        'icon': Icons.edit,
        'title': 'Image Editing',
        'description': 'Upload and edit existing images with AI',
      },
      {
        'icon': Icons.auto_fix_high,
        'title': 'Variations',
        'description': 'Create multiple versions of your images',
      },
      {
        'icon': Icons.offline_bolt,
        'title': 'Offline Mode',
        'description': 'Generate images locally on supported devices',
      },
    ];

    return CardStyles.outlined(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Features',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryGreen,
            ),
          ),
          const SizedBox(height: 12),
          ...features.map(
            (feature) => Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.cardColor.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    feature['icon'] as IconData,
                    color: AppTheme.primaryGreen,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          feature['title'] as String,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          feature['description'] as String,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.hintColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds enhanced image thumbnails with better styling
  Widget _buildEnhancedImageThumbnails() {
    return CardStyles.outlined(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.image, color: AppTheme.primaryGreen, size: 20),
              const SizedBox(width: 8),
              Text(
                'Selected Images',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryGreen,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(Icons.clear, size: 18),
                onPressed: () {
                  setState(() {
                    _image = null;
                    _mask = null;
                  });
                },
                tooltip: 'Clear images',
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              if (_image != null) ...[
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      _image!,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Source Image',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.primaryGreen,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
              if (_image != null && _mask != null) const SizedBox(width: 16),
              if (_mask != null) ...[
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      _mask!,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Mask Image',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.secondaryGreen,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// Builds the enhanced input area with modern styling
  Widget _buildEnhancedInputArea(
    BuildContext context,
    ThemeData theme,
    bool isDark,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
        border: Border(
          top: BorderSide(
            color: isDark ? AppTheme.darkOutline : AppTheme.lightOutline,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Image operation controls (when image is selected)
              if (_image != null) _buildImageOperationControls(theme),

              // Input Row
              Container(
                decoration: BoxDecoration(
                  color: theme.cardColor,
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Image upload button
                    Container(
                      margin: const EdgeInsets.only(left: 8),
                      child: IconButton(
                        icon: Icon(
                          Icons.add_photo_alternate,
                          color: _image != null
                              ? AppTheme.secondaryGreen
                              : AppTheme.primaryGreen,
                        ),
                        tooltip: _image != null
                            ? 'Change image'
                            : 'Upload image for editing',
                        onPressed: _pickImage,
                      ),
                    ),

                    // Text input field
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                        child: TextFormField(
                          maxLines: null,
                          minLines: 1,
                          focusNode: _inputFocusNode,
                          key: const Key("GenerateImageInputTextField"),
                          controller: _textController,
                          onChanged: (value) {
                            setState(() {
                              _wordCount = Util_Token.countTokens(value.trim());
                              _letterCount = value.trim().length;
                            });
                          },
                          onFieldSubmitted: (value) {
                            if (value.trim().isNotEmpty) {
                              _handleSendMessage();
                            }
                          },
                          decoration: InputDecoration(
                            hintText: _getInputHintText(),
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            hintStyle: TextStyle(color: theme.hintColor),
                          ),
                          style: theme.textTheme.bodyLarge,
                        ),
                      ),
                    ),

                    // Send button or loading indicator
                    Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: _isLoading
                          ? Container(
                              width: 40,
                              height: 40,
                              padding: const EdgeInsets.all(8),
                              child: LoadingStyles.primary(size: 24),
                            )
                          : IconButton(
                              key: const Key("GenerateImageSendRequestButton"),
                              icon: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: _canSendMessage()
                                      ? AppTheme.primaryGreen
                                      : theme.disabledColor,
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  _getActionIcon(),
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                              tooltip: _getActionTooltip(),
                              onPressed: _canSendMessage()
                                  ? _handleSendMessage
                                  : null,
                            ),
                    ),
                  ],
                ),
              ),

              // Word and character count
              if (_wordCount > 0 || _letterCount > 0)
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Words: $_wordCount',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text(
                        'Characters: $_letterCount',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds image operation controls when an image is selected
  Widget _buildImageOperationControls(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: CardStyles.outlined(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.tune, color: AppTheme.primaryGreen, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Image Operations',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryGreen,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildOperationChip(
                    'Edit Image',
                    ImageOperation.editImage,
                    Icons.edit,
                    theme,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildOperationChip(
                    'Create Variations',
                    ImageOperation.createVariations,
                    Icons.auto_fix_high,
                    theme,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildOperationChip(
                    'Generate New',
                    null,
                    Icons.auto_awesome,
                    theme,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Builds operation selection chips
  Widget _buildOperationChip(
    String label,
    ImageOperation? operation,
    IconData icon,
    ThemeData theme,
  ) {
    final isSelected = _selectedOperation == operation;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedOperation = operation;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryGreen.withValues(alpha: 0.1)
              : theme.cardColor,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppTheme.primaryGreen : theme.dividerColor,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? AppTheme.primaryGreen : theme.hintColor,
            ),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isSelected ? AppTheme.primaryGreen : theme.hintColor,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Gets appropriate hint text based on current state
  String _getInputHintText() {
    if (_image != null) {
      switch (_selectedOperation) {
        case ImageOperation.editImage:
          return "Describe how you want to edit this image...";
        case ImageOperation.createVariations:
          return "Describe variations you want to create...";
        default:
          return "Describe the new image you want to create...";
      }
    }
    return "Describe the image you want to create...";
  }

  /// Gets appropriate action icon based on current state
  IconData _getActionIcon() {
    if (_image != null) {
      switch (_selectedOperation) {
        case ImageOperation.editImage:
          return Icons.edit;
        case ImageOperation.createVariations:
          return Icons.auto_fix_high;
        default:
          return Icons.auto_awesome;
      }
    }
    return Icons.auto_awesome;
  }

  /// Gets appropriate tooltip based on current state
  String _getActionTooltip() {
    if (_image != null) {
      switch (_selectedOperation) {
        case ImageOperation.editImage:
          return 'Edit image';
        case ImageOperation.createVariations:
          return 'Create variations';
        default:
          return 'Generate new image';
      }
    }
    return 'Generate image';
  }

  /// Checks if message can be sent
  bool _canSendMessage() {
    final hasText = _textController.text.trim().isNotEmpty;
    if (_image != null && _selectedOperation != null) {
      return hasText || _selectedOperation == ImageOperation.createVariations;
    }
    return hasText;
  }

  /// Handles sending message with appropriate action
  void _handleSendMessage() {
    final message = _textController.text.trim();

    if (_image != null && _selectedOperation != null) {
      switch (_selectedOperation!) {
        case ImageOperation.editImage:
          _editImage();
          break;
        case ImageOperation.createVariations:
          _createVariations();
          break;
      }
    } else {
      if (message.isNotEmpty) {
        _sendMessage(message, usageType);
      }
    }

    _textController.clear();
    setState(() {
      _wordCount = 0;
      _letterCount = 0;
    });
  }
}
