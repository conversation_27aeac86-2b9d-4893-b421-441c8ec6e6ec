import 'package:diogeneschatbot/constants/constants.dart';
import 'package:diogeneschatbot/services/auth_service.dart';
import 'package:diogeneschatbot/widgets/product_info_widget.dart';
import 'package:diogeneschatbot/widgets/signup_signin_form_widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class LoginSignupPage extends StatefulWidget {
  const LoginSignupPage();

  @override
  _LoginSignupPageState createState() => _LoginSignupPageState();
}

class _LoginSignupPageState extends State<LoginSignupPage> {
  final AuthService _authService = AuthService();

  void _showInfoDialog(BuildContext context, String title, String content) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(title),
          content: SingleChildScrollView(child: Text(content)),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Close'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Add the image to the Row
            InkWell(
              onTap: () {
                // Handle the onTap event here (e.g., navigate to another page)
              },
              child: Image.asset(
                'assets/images/chat_icon.png',
                width: 25,
                height: 25,
              ),
            ),
            // Add some horizontal spacing between the image and the text
            SizedBox(width: 8.0, height: 5),
            // Add the text title next to the image
            Text(appTitle, style: TextStyle(fontSize: 12)),
          ],
        ),
        actions: kIsWeb
            ? [
                ElevatedButton(
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        content: Container(
                          constraints: BoxConstraints(
                            minWidth: 300,
                            maxWidth: 300,
                            maxHeight: 500,
                          ),
                          // Set a minimum width
                          child: SignInSignUpForm(authService: _authService),
                        ),
                      ),
                    );
                  },
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all<Color>(
                      Colors.orange,
                    ), // Set a different color
                  ),
                  child: Text('Log In'),
                ),
              ]
            : null,
      ),
      body: kIsWeb
          ? Center(child: ProductInfo())
          : Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Image.asset(
                    'assets/images/chat_icon.png',
                    width: 140,
                    height: 140,
                  ),
                ),
                Expanded(child: SignInSignUpForm(authService: _authService)),
                Wrap(
                  alignment: WrapAlignment.center,
                  spacing: 8.0,
                  children: [
                    TextButton(
                      onPressed: () {
                        _showInfoDialog(
                          context,
                          'Product Description',
                          productInfo, // Replace with your product description
                        );
                      },
                      child: Text('Product Info'),
                    ),
                    TextButton(
                      onPressed: () {
                        _showInfoDialog(
                          context,
                          'Privacy Notice',
                          privacyNotice, // Replace with your product description
                        );
                      },
                      child: Text('Privacy Notice'),
                    ),
                    TextButton(
                      onPressed: () {
                        _showInfoDialog(
                          context,
                          'Terms of Service',
                          termsOfService, // Replace with your product description
                        );
                      },
                      child: Text('Terms of Service'),
                    ),
                  ],
                ),
              ],
            ),
    );
  }
}
