import 'package:diogeneschatbot/bloc/timeline_bloc.dart';
import 'package:diogeneschatbot/models/profile.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/widgets/post_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TimelinePage extends StatefulWidget {
  final List<String> followingIds;
  final String currentUserId;

  TimelinePage({required this.followingIds, required this.currentUserId});

  @override
  _TimelinePageState createState() => _TimelinePageState();
}

class _TimelinePageState extends State<TimelinePage> {
  late TimelineBloc _timelineBloc;
  bool _isLoading = true;

  ProfileRepository profileRepository = ProfileRepository();

  @override
  void initState() {
    super.initState();
    _timelineBloc = BlocProvider.of<TimelineBloc>(context);
    _timelineBloc.add(LoadTimelinePosts());
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        gradient: isDark ? AppTheme.darkGradient : AppTheme.lightGradient,
      ),
      child: BlocListener<TimelineBloc, TimelineState>(
        listener: (context, state) {
          if (state is TimelineLoaded || state is TimelineError) {
            setState(() {
              _isLoading = false;
            });
          }
        },
        child: BlocBuilder<TimelineBloc, TimelineState>(
          builder: (context, state) {
            if (_isLoading) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.primaryGreen,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Loading your timeline...',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.7,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            } else if (state is TimelineLoaded) {
              logger.d('TimelineLoaded state.posts: ${state.posts.length}');
              if (state.posts.isEmpty) {
                return _buildEmptyState(context);
              } else {
                return RefreshIndicator(
                  onRefresh: () async {
                    _timelineBloc.add(LoadTimelinePosts());
                  },
                  child: PostsList(
                    posts: state.posts,
                    currentUserId: widget.currentUserId,
                  ),
                );
              }
            } else {
              return _buildErrorState(context);
            }
          },
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.timeline_outlined,
              size: 80,
              color: AppTheme.primaryGreen.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 24),
            Text(
              'Your Timeline is Empty',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Follow some users to see their posts in your timeline',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                // TODO: Navigate to discover users page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Discover users feature coming soon!'),
                  ),
                );
              },
              icon: const Icon(Icons.person_add_rounded),
              label: const Text('Discover Users'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
            const SizedBox(height: 16),
            Text(
              'Error Loading Timeline',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Something went wrong while loading your timeline',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                });
                _timelineBloc.add(LoadTimelinePosts());
              },
              icon: const Icon(Icons.refresh_rounded),
              label: const Text('Try Again'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
