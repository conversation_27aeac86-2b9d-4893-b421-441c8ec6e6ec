import 'package:diogeneschatbot/models/chat_room.dart';
import 'package:diogeneschatbot/models/profile.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/avatar_widget.dart';
import 'package:diogeneschatbot/widgets/media_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

/// Enhanced chat bubble widget with modern styling and user-friendly features
class ChatFriendsBubbleWidget extends StatefulWidget {
  const ChatFriendsBubbleWidget({
    super.key,
    required this.context,
    required this.message,
    required this.senderProfile,
    required this.isCurrentUser,
  });

  final BuildContext context;
  final Message message;
  final Profile? senderProfile;
  final bool isCurrentUser;

  @override
  State<ChatFriendsBubbleWidget> createState() =>
      _ChatFriendsBubbleWidgetState();
}

class _ChatFriendsBubbleWidgetState extends State<ChatFriendsBubbleWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _showTimestamp = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _showMessageOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildMessageOptionsSheet(),
    );
  }

  Widget _buildMessageOptionsSheet() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // Options
          ListTile(
            leading: Icon(Icons.copy, color: AppTheme.primaryGreen),
            title: const Text('Copy Message'),
            onTap: () {
              Clipboard.setData(ClipboardData(text: widget.message.content));
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Message copied to clipboard'),
                  backgroundColor: AppTheme.primaryGreen,
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
          if (widget.message.messageType == ChatMessageType.text)
            ListTile(
              leading: Icon(Icons.reply, color: AppTheme.accentBlue),
              title: const Text('Reply'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement reply functionality
              },
            ),
          ListTile(
            leading: Icon(Icons.info_outline, color: AppTheme.accentCoral),
            title: const Text('Message Info'),
            onTap: () {
              Navigator.pop(context);
              _showMessageInfo();
            },
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  void _showMessageInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.info_outline, color: AppTheme.primaryGreen),
            const SizedBox(width: 8),
            const Text('Message Info'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow(
              'Sender',
              widget.message.isFromAi
                  ? 'AI Assistant'
                  : widget.senderProfile?.name ?? 'Unknown',
            ),
            _buildInfoRow(
              'Sent',
              DateFormat(
                'MMM dd, yyyy at hh:mm a',
              ).format(widget.message.timestamp),
            ),
            _buildInfoRow(
              'Type',
              widget.message.messageType.toString().split('.').last,
            ),
            if (widget.message.messageId.isNotEmpty)
              _buildInfoRow('Message ID', widget.message.messageId),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return ScaleTransition(
      scale: _scaleAnimation,
      child: GestureDetector(
        onTap: () {
          setState(() {
            _showTimestamp = !_showTimestamp;
          });
        },
        onLongPress: _showMessageOptions,
        child: Container(
          margin: EdgeInsets.only(
            left: widget.isCurrentUser ? 50 : 8,
            right: widget.isCurrentUser ? 8 : 50,
            bottom: 8,
          ),
          child: Column(
            crossAxisAlignment: widget.isCurrentUser
                ? CrossAxisAlignment.end
                : CrossAxisAlignment.start,
            children: [
              // Sender info (only for non-current users or AI)
              if (!widget.isCurrentUser || widget.message.isFromAi)
                _buildSenderInfo(theme, isDark),

              // Message bubble
              _buildMessageBubble(theme, isDark),

              // Timestamp (shown on tap)
              if (_showTimestamp) _buildTimestamp(theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSenderInfo(ThemeData theme, bool isDark) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4, left: 12, right: 12),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.message.isFromAi)
            Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.smart_toy_rounded,
                size: 16,
                color: Colors.white,
              ),
            )
          else if (widget.senderProfile != null)
            SizedBox(
              width: 20,
              height: 20,
              child: AvatarWidget(profile: widget.senderProfile!),
            ),
          const SizedBox(width: 6),
          Text(
            widget.message.isFromAi
                ? "AI Assistant"
                : (widget.senderProfile?.name ?? "User"),
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ThemeData theme, bool isDark) {
    final isCurrentUser = widget.isCurrentUser;
    final isAI = widget.message.isFromAi;

    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.75,
      ),
      decoration: BoxDecoration(
        gradient: _getBubbleGradient(isCurrentUser, isAI, isDark),
        borderRadius: _getBubbleBorderRadius(isCurrentUser),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: widget.message.messageType == ChatMessageType.text
            ? _buildTextContent(theme, isCurrentUser, isAI)
            : _buildMediaContent(),
      ),
    );
  }

  Widget _buildTextContent(ThemeData theme, bool isCurrentUser, bool isAI) {
    final textColor = isCurrentUser || isAI
        ? Colors.white
        : theme.colorScheme.onSurface;

    return Text(
      widget.message.content,
      style: theme.textTheme.bodyMedium?.copyWith(
        color: textColor,
        height: 1.4,
      ),
    );
  }

  Widget _buildMediaContent() {
    return MediaList.RenderMedia(context, widget.message.content);
  }

  Widget _buildTimestamp(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(top: 4, left: 12, right: 12),
      child: Text(
        DateFormat('MMM dd, hh:mm a').format(widget.message.timestamp),
        style: theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          fontSize: 11,
        ),
      ),
    );
  }

  Gradient _getBubbleGradient(bool isCurrentUser, bool isAI, bool isDark) {
    if (isAI) {
      return AppTheme.primaryGradient;
    } else if (isCurrentUser) {
      return AppTheme.accentGradient;
    } else {
      return LinearGradient(
        colors: [
          isDark ? AppTheme.darkSurfaceVariant : AppTheme.lightSurfaceVariant,
          isDark ? AppTheme.darkSurfaceVariant : AppTheme.lightSurfaceVariant,
        ],
      );
    }
  }

  BorderRadius _getBubbleBorderRadius(bool isCurrentUser) {
    return BorderRadius.only(
      topLeft: const Radius.circular(20),
      topRight: const Radius.circular(20),
      bottomLeft: Radius.circular(isCurrentUser ? 20 : 4),
      bottomRight: Radius.circular(isCurrentUser ? 4 : 20),
    );
  }
}
