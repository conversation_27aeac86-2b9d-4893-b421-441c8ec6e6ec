import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:diogeneschatbot/models/chat_room.dart';
import 'package:diogeneschatbot/models/profile.dart';
import 'package:diogeneschatbot/widgets/chat_friends_bubble_widget.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class ChatFriendsMessageItemWidget extends StatelessWidget {
  const ChatFriendsMessageItemWidget({
    super.key,
    required ProfileRepository profileRepository,
    required this.onChatHistoryUpdate,
    required this.snapshot,
    required this.index,
  }) : _profileRepository = profileRepository;

  final ProfileRepository _profileRepository;
  final Function(String username, String message) onChatHistoryUpdate;
  final AsyncSnapshot<QuerySnapshot<Object?>> snapshot;
  final int index;

  @override
  Widget build(BuildContext context) {
    try {
      // Validate data before processing
      if (snapshot.data == null ||
          snapshot.data!.docs.isEmpty ||
          index >= snapshot.data!.docs.length) {
        return const Padding(
          padding: EdgeInsets.all(8.0),
          child: Text("Invalid message data"),
        );
      }

      Message message = Message.fromFirestore(
        snapshot.data!.docs[snapshot.data!.docs.length - 1 - index],
      );
      bool isCurrentUser =
          message.senderId == FirebaseAuth.instance.currentUser!.uid;

      return FutureBuilder<Profile?>(
        future: _profileRepository.getProfile(message.senderId),
        builder: (BuildContext context, AsyncSnapshot<Profile?> profileSnapshot) {
          if (profileSnapshot.connectionState == ConnectionState.waiting) {
            return const Padding(
              padding: EdgeInsets.all(8.0),
              child: Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            );
          }

          if (profileSnapshot.hasError) {
            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                "Error loading profile",
                style: TextStyle(color: Colors.red),
              ),
            );
          }

          if (profileSnapshot.hasData) {
            Profile? senderProfile = profileSnapshot.data;
            String formattedTimestamp = DateFormat(
              'hh:mm a',
            ).format(message.timestamp);
            String chatMessage =
                "${message.isFromAi ? 'AI' : ''}${senderProfile?.name ?? "User Name"} at $formattedTimestamp: ${message.content}";

            // Use callback instead of direct mutation
            onChatHistoryUpdate(
              senderProfile?.name ?? "User Name",
              chatMessage,
            );

            return ChatFriendsBubbleWidget(
              context: context,
              message: message,
              senderProfile: senderProfile,
              isCurrentUser: isCurrentUser,
            );
          }

          return const Padding(
            padding: EdgeInsets.all(8.0),
            child: Text("No profile data found"),
          );
        },
      );
    } catch (e) {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Text(
          "Error loading message: $e",
          style: TextStyle(color: Colors.red),
        ),
      );
    }
  }
}
