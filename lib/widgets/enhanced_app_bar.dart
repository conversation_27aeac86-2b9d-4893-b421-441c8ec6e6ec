import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';

/// Enhanced app bar with gradient backgrounds and modern styling
class EnhancedAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final Gradient? gradient;
  final Color? backgroundColor;
  final double elevation;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final PreferredSizeWidget? bottom;
  final double? titleSpacing;
  final TextStyle? titleStyle;
  final bool automaticallyImplyLeading;

  const EnhancedAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.gradient,
    this.backgroundColor,
    this.elevation = 0,
    this.showBackButton = true,
    this.onBackPressed,
    this.bottom,
    this.titleSpacing,
    this.titleStyle,
    this.automaticallyImplyLeading = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    final defaultGradient = gradient ?? AppTheme.primaryGradient;
    final textColor = _getTextColor(isDark);

    return Container(
      decoration: BoxDecoration(
        gradient: defaultGradient,
        boxShadow: elevation > 0
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: elevation * 2,
                  offset: Offset(0, elevation),
                ),
              ]
            : null,
      ),
      child: AppBar(
        title: Text(
          title,
          style: titleStyle ??
              theme.textTheme.titleLarge?.copyWith(
                color: textColor,
                fontWeight: FontWeight.w600,
              ),
        ),
        centerTitle: centerTitle,
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: leading ??
            (showBackButton && Navigator.of(context).canPop()
                ? IconButton(
                    icon: Icon(Icons.arrow_back, color: textColor),
                    onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                  )
                : null),
        actions: actions?.map((action) => _wrapActionWithColor(action, textColor)).toList(),
        automaticallyImplyLeading: automaticallyImplyLeading,
        titleSpacing: titleSpacing,
        bottom: bottom,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: _getStatusBarIconBrightness(isDark),
          statusBarBrightness: _getStatusBarBrightness(isDark),
        ),
      ),
    );
  }

  Widget _wrapActionWithColor(Widget action, Color color) {
    if (action is IconButton) {
      return IconButton(
        icon: action.icon,
        onPressed: action.onPressed,
        color: color,
        tooltip: action.tooltip,
      );
    }
    return action;
  }

  Color _getTextColor(bool isDark) {
    if (gradient != null) {
      // For gradient backgrounds, use white text for better contrast
      return Colors.white;
    }
    return isDark ? Colors.white : Colors.black;
  }

  Brightness _getStatusBarIconBrightness(bool isDark) {
    if (gradient != null) {
      return Brightness.light; // White icons for gradient backgrounds
    }
    return isDark ? Brightness.light : Brightness.dark;
  }

  Brightness _getStatusBarBrightness(bool isDark) {
    if (gradient != null) {
      return Brightness.dark; // Dark status bar for gradient backgrounds
    }
    return isDark ? Brightness.dark : Brightness.light;
  }

  @override
  Size get preferredSize => Size.fromHeight(
        kToolbarHeight + (bottom?.preferredSize.height ?? 0.0),
      );
}

/// Predefined app bar styles for common use cases
class AppBarStyles {
  static EnhancedAppBar primary({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
    VoidCallback? onBackPressed,
    PreferredSizeWidget? bottom,
  }) {
    return EnhancedAppBar(
      title: title,
      actions: actions,
      leading: leading,
      centerTitle: centerTitle,
      gradient: AppTheme.primaryGradient,
      onBackPressed: onBackPressed,
      bottom: bottom,
    );
  }

  static EnhancedAppBar accent({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
    VoidCallback? onBackPressed,
    PreferredSizeWidget? bottom,
  }) {
    return EnhancedAppBar(
      title: title,
      actions: actions,
      leading: leading,
      centerTitle: centerTitle,
      gradient: AppTheme.accentGradient,
      onBackPressed: onBackPressed,
      bottom: bottom,
    );
  }

  static EnhancedAppBar transparent({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
    VoidCallback? onBackPressed,
    PreferredSizeWidget? bottom,
    Color? textColor,
  }) {
    return EnhancedAppBar(
      title: title,
      actions: actions,
      leading: leading,
      centerTitle: centerTitle,
      backgroundColor: Colors.transparent,
      elevation: 0,
      onBackPressed: onBackPressed,
      bottom: bottom,
      titleStyle: TextStyle(
        color: textColor ?? Colors.black,
        fontWeight: FontWeight.w600,
        fontSize: 20,
      ),
    );
  }

  static EnhancedAppBar elevated({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
    VoidCallback? onBackPressed,
    PreferredSizeWidget? bottom,
    Color? backgroundColor,
  }) {
    return EnhancedAppBar(
      title: title,
      actions: actions,
      leading: leading,
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? AppTheme.primaryGreen,
      elevation: 8,
      onBackPressed: onBackPressed,
      bottom: bottom,
    );
  }
}

/// Enhanced search app bar with search functionality
class EnhancedSearchAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String hintText;
  final ValueChanged<String>? onSearchChanged;
  final VoidCallback? onSearchSubmitted;
  final VoidCallback? onClearPressed;
  final List<Widget>? actions;
  final Gradient? gradient;
  final bool autofocus;

  const EnhancedSearchAppBar({
    super.key,
    this.hintText = 'Search...',
    this.onSearchChanged,
    this.onSearchSubmitted,
    this.onClearPressed,
    this.actions,
    this.gradient,
    this.autofocus = false,
  });

  @override
  State<EnhancedSearchAppBar> createState() => _EnhancedSearchAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _EnhancedSearchAppBarState extends State<EnhancedSearchAppBar> {
  late TextEditingController _searchController;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _focusNode = FocusNode();
    
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final defaultGradient = widget.gradient ?? AppTheme.primaryGradient;

    return Container(
      decoration: BoxDecoration(gradient: defaultGradient),
      child: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: TextField(
          controller: _searchController,
          focusNode: _focusNode,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: widget.hintText,
            hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.7)),
            border: InputBorder.none,
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear, color: Colors.white),
                    onPressed: () {
                      _searchController.clear();
                      widget.onClearPressed?.call();
                      setState(() {});
                    },
                  )
                : null,
          ),
          onChanged: (value) {
            widget.onSearchChanged?.call(value);
            setState(() {});
          },
          onSubmitted: (value) {
            widget.onSearchSubmitted?.call();
          },
        ),
        actions: widget.actions,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
        ),
      ),
    );
  }
}
