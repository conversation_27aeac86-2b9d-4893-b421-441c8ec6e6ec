import 'package:flutter/material.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';

/// Enhanced drawer with modern styling and animations
class EnhancedDrawer extends StatelessWidget {
  final Widget? header;
  final List<DrawerItem> items;
  final Widget? footer;
  final Color? backgroundColor;
  final Gradient? gradient;
  final double? width;

  const EnhancedDrawer({
    super.key,
    this.header,
    required this.items,
    this.footer,
    this.backgroundColor,
    this.gradient,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      width: width ?? 280,
      decoration: BoxDecoration(
        color: gradient == null
            ? (backgroundColor ?? (isDark ? AppTheme.darkSurface : AppTheme.lightSurface))
            : null,
        gradient: gradient,
      ),
      child: Safe<PERSON>rea(
        child: Column(
          children: [
            if (header != null) header!,
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(vertical: 8),
                children: items.map((item) => _buildDrawerItem(context, item)).toList(),
              ),
            ),
            if (footer != null) footer!,
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerItem(BuildContext context, DrawerItem item) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    if (item.isDivider) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Divider(
          color: isDark ? AppTheme.darkOutline : AppTheme.lightOutline,
        ),
      );
    }

    if (item.isHeader) {
      return Padding(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
        child: Text(
          item.title!,
          style: theme.textTheme.titleSmall?.copyWith(
            color: AppTheme.primaryGreen,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: AppTheme.mediumRadius,
        color: item.isSelected
            ? AppTheme.primaryGreen.withValues(alpha: 0.1)
            : Colors.transparent,
      ),
      child: ListTile(
        leading: item.icon != null
            ? Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: item.isSelected
                      ? AppTheme.primaryGreen
                      : AppTheme.primaryGreen.withValues(alpha: 0.1),
                ),
                child: Icon(
                  item.icon,
                  color: item.isSelected
                      ? Colors.white
                      : AppTheme.primaryGreen,
                  size: 20,
                ),
              )
            : null,
        title: Text(
          item.title!,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: item.isSelected
                ? AppTheme.primaryGreen
                : (isDark ? AppTheme.darkOnSurface : AppTheme.lightOnSurface),
            fontWeight: item.isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
        subtitle: item.subtitle != null
            ? Text(
                item.subtitle!,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isDark
                      ? AppTheme.darkOnSurface.withValues(alpha: 0.7)
                      : AppTheme.lightOnSurface.withValues(alpha: 0.7),
                ),
              )
            : null,
        trailing: item.trailing ??
            (item.badge != null
                ? Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryGreen,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      item.badge!,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  )
                : null),
        onTap: item.onTap,
        shape: RoundedRectangleBorder(
          borderRadius: AppTheme.mediumRadius,
        ),
      ),
    );
  }
}

/// Drawer item model
class DrawerItem {
  final String? title;
  final String? subtitle;
  final IconData? icon;
  final VoidCallback? onTap;
  final Widget? trailing;
  final String? badge;
  final bool isSelected;
  final bool isDivider;
  final bool isHeader;

  const DrawerItem({
    this.title,
    this.subtitle,
    this.icon,
    this.onTap,
    this.trailing,
    this.badge,
    this.isSelected = false,
    this.isDivider = false,
    this.isHeader = false,
  });

  factory DrawerItem.divider() {
    return const DrawerItem(isDivider: true);
  }

  factory DrawerItem.header(String title) {
    return DrawerItem(title: title, isHeader: true);
  }
}

/// Enhanced drawer header with user profile
class EnhancedDrawerHeader extends StatelessWidget {
  final String? userName;
  final String? userEmail;
  final Widget? userAvatar;
  final VoidCallback? onProfileTap;
  final Gradient? gradient;
  final Color? backgroundColor;

  const EnhancedDrawerHeader({
    super.key,
    this.userName,
    this.userEmail,
    this.userAvatar,
    this.onProfileTap,
    this.gradient,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final defaultGradient = gradient ?? AppTheme.primaryGradient;

    return Container(
      height: 200,
      decoration: BoxDecoration(
        gradient: defaultGradient,
      ),
      child: InkWell(
        onTap: onProfileTap,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              userAvatar ??
                  CircleAvatar(
                    radius: 35,
                    backgroundColor: Colors.white.withValues(alpha: 0.2),
                    child: Icon(
                      Icons.person,
                      size: 40,
                      color: Colors.white,
                    ),
                  ),
              const SizedBox(height: 16),
              if (userName != null)
                Text(
                  userName!,
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              if (userEmail != null) ...[
                const SizedBox(height: 4),
                Text(
                  userEmail!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Predefined drawer styles for common use cases
class DrawerStyles {
  static EnhancedDrawer primary({
    required List<DrawerItem> items,
    Widget? header,
    Widget? footer,
  }) {
    return EnhancedDrawer(
      header: header,
      items: items,
      footer: footer,
      gradient: AppTheme.lightGradient,
    );
  }

  static EnhancedDrawer dark({
    required List<DrawerItem> items,
    Widget? header,
    Widget? footer,
  }) {
    return EnhancedDrawer(
      header: header,
      items: items,
      footer: footer,
      backgroundColor: AppTheme.darkSurface,
    );
  }

  static List<DrawerItem> defaultItems({
    required VoidCallback onHomeTap,
    required VoidCallback onProfileTap,
    required VoidCallback onSettingsTap,
    required VoidCallback onLogoutTap,
    String? notificationBadge,
  }) {
    return [
      DrawerItem(
        title: 'Home',
        icon: Icons.home,
        onTap: onHomeTap,
        isSelected: true,
      ),
      DrawerItem(
        title: 'Profile',
        icon: Icons.person,
        onTap: onProfileTap,
      ),
      DrawerItem(
        title: 'Notifications',
        icon: Icons.notifications,
        onTap: () {},
        badge: notificationBadge,
      ),
      DrawerItem.divider(),
      DrawerItem.header('Settings'),
      DrawerItem(
        title: 'Preferences',
        icon: Icons.settings,
        onTap: onSettingsTap,
      ),
      DrawerItem(
        title: 'Help & Support',
        icon: Icons.help,
        onTap: () {},
      ),
      DrawerItem.divider(),
      DrawerItem(
        title: 'Logout',
        icon: Icons.logout,
        onTap: onLogoutTap,
      ),
    ];
  }
}
