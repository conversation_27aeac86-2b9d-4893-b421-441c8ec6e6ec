import 'package:flutter/material.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';

/// Enhanced loading indicators with modern animations
class EnhancedLoading extends StatefulWidget {
  final LoadingType type;
  final double size;
  final Color? color;
  final String? message;
  final bool showMessage;

  const EnhancedLoading({
    super.key,
    this.type = LoadingType.circular,
    this.size = 40,
    this.color,
    this.message,
    this.showMessage = false,
  });

  @override
  State<EnhancedLoading> createState() => _EnhancedLoadingState();
}

class _EnhancedLoadingState extends State<EnhancedLoading>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _pulseController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.linear,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _controller.repeat();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final loadingColor = widget.color ?? AppTheme.primaryGreen;

    Widget loadingWidget;

    switch (widget.type) {
      case LoadingType.circular:
        loadingWidget = _buildCircularLoading(loadingColor);
        break;
      case LoadingType.dots:
        loadingWidget = _buildDotsLoading(loadingColor);
        break;
      case LoadingType.pulse:
        loadingWidget = _buildPulseLoading(loadingColor);
        break;
      case LoadingType.wave:
        loadingWidget = _buildWaveLoading(loadingColor);
        break;
      case LoadingType.gradient:
        loadingWidget = _buildGradientLoading();
        break;
    }

    if (widget.showMessage && widget.message != null) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          loadingWidget,
          const SizedBox(height: 16),
          Text(
            widget.message!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: loadingColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    return loadingWidget;
  }

  Widget _buildCircularLoading(Color color) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: CircularProgressIndicator(
        strokeWidth: 3,
        valueColor: AlwaysStoppedAnimation<Color>(color),
      ),
    );
  }

  Widget _buildDotsLoading(Color color) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(3, (index) {
            final delay = index * 0.2;
            final animationValue = (_controller.value + delay) % 1.0;
            final scale = 0.5 + 0.5 * (1 - (animationValue - 0.5).abs() * 2);
            
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 2),
              child: Transform.scale(
                scale: scale,
                child: Container(
                  width: widget.size / 4,
                  height: widget.size / 4,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }

  Widget _buildPulseLoading(Color color) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.3),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Container(
                width: widget.size * 0.6,
                height: widget.size * 0.6,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildWaveLoading(Color color) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(4, (index) {
            final delay = index * 0.1;
            final animationValue = (_controller.value + delay) % 1.0;
            final height = widget.size * (0.3 + 0.7 * (1 - (animationValue - 0.5).abs() * 2));
            
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 1),
              width: widget.size / 8,
              height: height,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(widget.size / 16),
              ),
            );
          }),
        );
      },
    );
  }

  Widget _buildGradientLoading() {
    return AnimatedBuilder(
      animation: _rotationAnimation,
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotationAnimation.value * 2 * 3.14159,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: SweepGradient(
                colors: [
                  AppTheme.primaryGreen,
                  AppTheme.secondaryGreen,
                  AppTheme.accentBlue,
                  AppTheme.primaryGreen,
                ],
                stops: const [0.0, 0.3, 0.7, 1.0],
              ),
            ),
            child: Container(
              margin: EdgeInsets.all(widget.size * 0.1),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).scaffoldBackgroundColor,
              ),
            ),
          ),
        );
      },
    );
  }
}

enum LoadingType {
  circular,
  dots,
  pulse,
  wave,
  gradient,
}

/// Predefined loading widgets for common use cases
class LoadingStyles {
  static Widget primary({
    double size = 40,
    String? message,
  }) {
    return EnhancedLoading(
      type: LoadingType.circular,
      size: size,
      color: AppTheme.primaryGreen,
      message: message,
      showMessage: message != null,
    );
  }

  static Widget dots({
    double size = 40,
    Color? color,
    String? message,
  }) {
    return EnhancedLoading(
      type: LoadingType.dots,
      size: size,
      color: color ?? AppTheme.primaryGreen,
      message: message,
      showMessage: message != null,
    );
  }

  static Widget pulse({
    double size = 60,
    Color? color,
    String? message,
  }) {
    return EnhancedLoading(
      type: LoadingType.pulse,
      size: size,
      color: color ?? AppTheme.primaryGreen,
      message: message,
      showMessage: message != null,
    );
  }

  static Widget wave({
    double size = 40,
    Color? color,
    String? message,
  }) {
    return EnhancedLoading(
      type: LoadingType.wave,
      size: size,
      color: color ?? AppTheme.primaryGreen,
      message: message,
      showMessage: message != null,
    );
  }

  static Widget gradient({
    double size = 50,
    String? message,
  }) {
    return EnhancedLoading(
      type: LoadingType.gradient,
      size: size,
      message: message,
      showMessage: message != null,
    );
  }

  /// Full screen loading overlay
  static Widget overlay({
    String? message,
    Color? backgroundColor,
  }) {
    return Container(
      color: backgroundColor ?? Colors.black.withValues(alpha: 0.5),
      child: Center(
        child: EnhancedLoading(
          type: LoadingType.gradient,
          size: 60,
          message: message ?? 'Loading...',
          showMessage: true,
        ),
      ),
    );
  }
}
