import 'package:diogeneschatbot/models/usage_chart_data.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class UsageChart extends StatefulWidget {
  final List<UsageChartData> data;
  final String? title;
  final Color? primaryColor;
  final Color? gradientStartColor;
  final Color? gradientEndColor;

  const UsageChart({
    super.key,
    required this.data,
    this.title,
    this.primaryColor,
    this.gradientStartColor,
    this.gradientEndColor,
  });

  @override
  State<UsageChart> createState() => _UsageChartState();
}

class _UsageChartState extends State<UsageChart>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  int? _touchedIndex;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.data.isEmpty) {
      return _buildEmptyState(context);
    }

    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final primaryColor = widget.primaryColor ?? AppTheme.primaryGreen;
    final gradientStart = widget.gradientStartColor ?? primaryColor;
    final gradientEnd =
        widget.gradientEndColor ?? primaryColor.withValues(alpha: 0.1);

    // Sort the data by the date field in ascending order
    final sortedData = List<UsageChartData>.from(widget.data)
      ..sort((a, b) => a.date.compareTo(b.date));

    // Define the minDate and maxDate based on sorted data
    final minDate = sortedData.first.date;
    final maxDate = sortedData.last.date;
    final maxValue = sortedData
        .map((e) => e.value)
        .reduce((a, b) => a > b ? a : b);

    // Determine how many labels to show
    final totalDays = maxDate.difference(minDate).inDays;
    final labelInterval = totalDays > 30 ? (totalDays / 5).round() : 1;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Column(
          children: [
            if (widget.title != null) ...[
              Text(
                widget.title!,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 16),
            ],
            Expanded(
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: false,
                    horizontalInterval: maxValue > 0 ? maxValue / 5 : 1,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: colorScheme.outline.withValues(alpha: 0.2),
                        strokeWidth: 1,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                        interval: maxValue > 0 ? maxValue / 5 : 1,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            value.toInt().toString(),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurface.withValues(
                                alpha: 0.6,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                        interval:
                            labelInterval.toDouble() * 24 * 60 * 60 * 1000,
                        getTitlesWidget: (value, meta) {
                          final date = DateTime.fromMillisecondsSinceEpoch(
                            value.toInt(),
                          );
                          final dateFormatter = totalDays > 30
                              ? DateFormat('MMM dd')
                              : DateFormat('dd');
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              dateFormatter.format(date),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurface.withValues(
                                  alpha: 0.6,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border(
                      bottom: BorderSide(
                        color: colorScheme.outline.withValues(alpha: 0.3),
                        width: 1,
                      ),
                      left: BorderSide(
                        color: colorScheme.outline.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                  ),
                  lineBarsData: [
                    LineChartBarData(
                      spots: sortedData.asMap().entries.map((entry) {
                        final animatedValue =
                            entry.value.value * _animation.value;
                        return FlSpot(
                          entry.value.date.millisecondsSinceEpoch.toDouble(),
                          animatedValue,
                        );
                      }).toList(),
                      isCurved: true,
                      curveSmoothness: 0.3,
                      color: primaryColor,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: _touchedIndex == index ? 6 : 4,
                            color: primaryColor,
                            strokeWidth: 2,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                      belowBarData: BarAreaData(
                        show: true,
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            gradientStart.withValues(alpha: 0.3),
                            gradientEnd,
                          ],
                        ),
                      ),
                    ),
                  ],
                  minX: minDate.millisecondsSinceEpoch.toDouble(),
                  maxX: maxDate.millisecondsSinceEpoch.toDouble(),
                  minY: 0,
                  maxY: maxValue > 0 ? maxValue * 1.1 : 10,
                  lineTouchData: LineTouchData(
                    enabled: true,
                    touchCallback:
                        (FlTouchEvent event, LineTouchResponse? touchResponse) {
                          setState(() {
                            if (touchResponse != null &&
                                touchResponse.lineBarSpots != null) {
                              _touchedIndex =
                                  touchResponse.lineBarSpots!.first.spotIndex;
                            } else {
                              _touchedIndex = null;
                            }
                          });
                        },
                    touchTooltipData: LineTouchTooltipData(
                      getTooltipColor: (touchedSpot) => colorScheme.surface,
                      getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                        return touchedBarSpots.map((barSpot) {
                          final date = DateTime.fromMillisecondsSinceEpoch(
                            barSpot.x.toInt(),
                          );
                          final value = barSpot.y.toInt();
                          return LineTooltipItem(
                            '${DateFormat('MMM dd, yyyy').format(date)}\n$value',
                            TextStyle(
                              color: colorScheme.onSurface,
                              fontWeight: FontWeight.bold,
                            ),
                          );
                        }).toList();
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.show_chart,
            size: 64,
            color: colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No usage data available',
            style: theme.textTheme.titleMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }
}
