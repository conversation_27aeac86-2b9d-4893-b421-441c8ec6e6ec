import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:diogeneschatbot/pages/usage_history_page.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';

void main() {
  group('UsageHistoryPage Tests', () {
    testWidgets('UsageHistoryPage renders correctly', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const UsageHistoryPage(userId: 'test-user-id'),
        ),
      );

      // Verify that the page renders
      expect(find.text('Usage Analytics'), findsOneWidget);
      expect(find.text('Loading usage data...'), findsOneWidget);
    });

    testWidgets('UsageHistoryPage shows loading state initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const UsageHistoryPage(userId: 'test-user-id'),
        ),
      );

      // Should show loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading usage data...'), findsOneWidget);
    });

    testWidgets('UsageHistoryPage has refresh and date range buttons', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const UsageHistoryPage(userId: 'test-user-id'),
        ),
      );

      // Should have refresh and date range buttons in app bar
      expect(find.byIcon(Icons.refresh), findsOneWidget);
      expect(find.byIcon(Icons.date_range), findsOneWidget);
    });
  });
}
